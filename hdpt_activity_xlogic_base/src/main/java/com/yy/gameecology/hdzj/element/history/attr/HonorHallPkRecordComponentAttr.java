package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021.09.23 17:31
 * 记录所有的pk记录
 */
public class HonorHallPkRecordComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "redis存储key")
    private String redisKey = "pk_record";

    /**
     * 涉及的榜单
     */
    @ComponentAttrField(labelText = "涉及的榜单", remark = "多个时英文逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class, labelText = "榜单id")
    })
    private List<Long> rankIds;

    /**
     * [榜单ID:阶段ID]
     */
    @ComponentAttrField(labelText = "阶段配置", remark = "配置格式：榜单ID:阶段ID,多个时英文逗号分隔",
            subFields = {
                    @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, labelText = "阶段配置")
            })
    private List<String> rankPhaseList;

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public List<Long> getRankIds() {
        return rankIds;
    }

    public void setRankIds(List<Long> rankIds) {
        this.rankIds = rankIds;
    }

    public List<String> getRankPhaseList() {
        return rankPhaseList;
    }

    public void setRankPhaseList(List<String> rankPhaseList) {
        this.rankPhaseList = rankPhaseList;
    }
}
