package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.bean.AwardAttrByComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * List<OT> 对象中还含有List，不符合规范，需要重构
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class GiftPackageV2ComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Integer busiId;

    /**
     * 是否需要限制用户的购买资格
     */
    @ComponentAttrField(labelText = "是否限制购买资格")
    private boolean limitQualification = true;

    /**
     * 限制允许购买或者不允许购买
     */
    @ComponentAttrField(labelText = "允许购买")
    private boolean allow = true;

    /**
     * 存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了
     */
    @ComponentAttrField(labelText = "用户名单缓存key", remark = "存放限制用户名单的redis set key ,需要直接制定key的全称，不会再加前缀了")
    private String limitUidRedisKey = "";
    /**
     * 被限制的提醒
     */
    @ComponentAttrField(labelText = "被限制的提醒文案")
    private String limitQualificationTip = "";

    /**
     * 限制用户购买数量，小于等于0是不限制
     */
    @ComponentAttrField(labelText = "限制购买数量", remark = "限制用户购买数量，小于等于0是不限制")
    private int limitBuyCount = 1;
    /**
     * 购买数量限制提示
     */
    @ComponentAttrField(labelText = "购买数量限制提示")
    private String limitBuyCountTip = "";

    /**
     * 同个ip限制购买的秒数
     */
    @ComponentAttrField(labelText = "同个ip限制秒数", remark = "同个ip限制购买的秒数")
    private long ipLimitSec = 60L;
    /**
     * 营收扣费标识，找营收配
     */
    @ComponentAttrField(labelText = "营收扣费标识", remark = "营收扣费标识，找营收配")
    private int productType;

    @ComponentAttrField(labelText = "产品id", remark = "产品id,保持默认值即可")
    private int productId = 0;

    /**
     * 礼包扣费价格（以紫宝石单位，1:1000）
     */
    @ComponentAttrField(labelText = "礼包价格", remark = "礼包扣费价格（以紫宝石单位，1:1000）")
    private long price = 100;
    /**
     * 礼包详情，传给营收
     */
    @ComponentAttrField(labelText = "礼包详情", remark = "礼包详情，传给营收")
    private String giftDes = "gift desc";

    @ComponentAttrField(labelText = "营收appId", remark = "默认根据业务id转换,无需填写")
    private int consumeAppId;

    @ComponentAttrField(labelText = "限制总上限")
    private boolean globalLimitCount;

    @ComponentAttrField(labelText = "同个ip购买数量限制")
    private long ipLimitCount = 1;

    @ComponentAttrField(labelText = "购买成功文案")
    private String successPromotion;
    // 抽奖奖品
    private List<AwardAttrByComponent> awards = Collections.emptyList();
}
