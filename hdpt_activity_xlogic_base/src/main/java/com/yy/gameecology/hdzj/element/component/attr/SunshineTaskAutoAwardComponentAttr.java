package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.SunshineTaskConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.attrconfig.TimeKeySource;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.10.15 10:52
 * 主播个人突破任务+全站高光任务
 */
@SkipCheck
@Data
public class SunshineTaskAutoAwardComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    private long timeKey;

    @ComponentAttrField(labelText = "奖池数量")
    private long awardPoolConfig;

    private String singleTaskName;

    private Long singleTaskLevel;

    @ComponentAttrField(labelText = "突破任务奖励比例")
    private String awardRate;

    private String awardName;

    private String awardIcon;

    @ComponentAttrField(labelText = "突破奖励上限", remark = "configScore * awardRate 的上限")
    private long singleAwardLimit;

    @ComponentAttrField(labelText = "任务消息重试时限", remark = "超过这个时间，榜单更新更新事件 redis seq失效不可安全重试")
    private Integer seqExpireSeconds = DateUtil.ONE_DAY_SECONDS;

    @ComponentAttrField(labelText = "角色全站任务配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "角色Id"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "任务类型(single是突破任务，系统内置，不能被占用)")
            })
    private Map<Long, String> roleIdCommonTaskType = Maps.newHashMap();


    @ComponentAttrField(labelText = "任务配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.VALUE, type = SunshineTaskConfig.class, labelText = "过任务配置")
            })
    private Map<String, Map<Long, SunshineTaskConfig>> taskConfig;


    @ComponentAttrField(labelText = "过任务广播配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务类型"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "任务等级"),
                    @SubField(fieldName = Constant.MAP_LIST_VALUE, type = BroadcastConfig.class, labelText = "任务等级")
            })
    private Map<String, Map<Long, List<BroadcastConfig>>> taskBanner;


    @ComponentAttrField(labelText = "默认横幅", remark = "过任务横幅默认url,如果levelBro没有配置则使用这个默认的")
    private String defaultBannerUrl;


    @ComponentAttrField(labelText = "任务提示文案")
    private String awardDescTips = "今日荣耀值达%s可完成%s任务";


    @ComponentAttrField(labelText = "角色奖励日限",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "角色Id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖励日限额", remark = "小于等于0代表不限制")
            })
    private Map<Long, Long> roleAwardDayLimit;


    @ComponentAttrField(labelText = "奖励的奖品",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "busiId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "taskId"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "packageId")
            })
    private Map<Long, Map<Long, Long>> busiTaskIdPackageId = Maps.newHashMap();

    @ComponentAttrField(labelText = "默认奖励的数量", remark = "奖池消耗完后默认发的奖励数量")
    private long defaultCount;

    @ComponentAttrField(labelText = "默认奖励的奖品", remark = "奖池消耗完后默认发的奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "busiId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "taskId"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "packageId")
            })
    private Map<Long, Map<Long, Long>> defaultTaskPackageId = Maps.newHashMap();

    private String defaultAwardName;

    private String defaultAwardIcon;

    @ComponentAttrField(labelText = "突破任务设置url", remark = "txt文本，格式为：uid,分数 换行，设置完成后可删除此属性，避免误操作。设置url:https://test-activity-ge.yy.com/cmpt/SunshineTaskComponentV3/initSingleTaskConfig?actId=2022081001&index=500")
    private String initSingleTaskUrl;

    @ComponentAttrField(labelText = "突破任务设置权限uid", remark = "多个uid逗号隔开"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> initSingleTaskAuthUid = Lists.newArrayList();
}
