package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

/**
 * <AUTHOR>
 * @date 2021.09.09 11:37
 */

public class FirstEnterTemComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id列表", remark = "多个逗号分隔，业务枚举：200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩", subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private long[] busiIds;
    /*  private long busiId;
    private long uid;
    private long sid;
    private long ssid;*/

    public long[] getBusiIds() {
        return busiIds;
    }

    public void setBusiIds(long[] busiIds) {
        this.busiIds = busiIds;
    }
}
