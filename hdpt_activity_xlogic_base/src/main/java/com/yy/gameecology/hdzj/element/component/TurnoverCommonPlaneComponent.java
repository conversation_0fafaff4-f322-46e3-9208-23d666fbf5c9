package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Receiver;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.TurnoverCommonPlaneEvent;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.BlackListComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.TurnoverCommonPlaneComponentAttr;
import com.yy.thrift.fts_room_manager.AntiPoachingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动礼物飞机横幅，需求文档：https://www.kdocs.cn/l/clmY5lj1kkmW
 *
 * <AUTHOR>
 * @date 2023.03.08 17:49
 * 营收送礼飞机事件处理,主要发移动端横幅
 */
@RequestMapping("/2048")
@RestController
@Component
public class TurnoverCommonPlaneComponent extends BaseActComponent<TurnoverCommonPlaneComponentAttr> {

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private BlackListComponent blackListComponent;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Override
    public Long getComponentId() {
        return ComponentId.TURNOVER_COMMON_PLANE;
    }

    @RequestMapping("/testBroAppBanner")
    public Response<String> testBroAppBanner(HttpServletRequest req, HttpServletResponse resp,
                         long actId, long index, int level, long propsId, long sid, long ssid) {
        if (SysEvHelper.isDeploy()) {
            if (!commonService.isGrey(actId)) {
                return Response.success("测试接口线上不执行");
            }
        }
        long uid = getLoginYYUid(req, resp);
        TurnoverCommonPlaneEvent event = new TurnoverCommonPlaneEvent();
        event.setSeqId(System.currentTimeMillis() + "");
        event.setSendUid(uid);
        event.setSid(sid);
        event.setSsid(ssid);
        event.setCompereUid(uid);
        event.setActId(actId);
        event.setAppId(34);
        event.setPlatform(0);
        event.setChannel(171);
        event.setSystemInfo(0);
        event.setPropsId(propsId);
        event.setCount(5200L);
        event.setComboHits(10L);
        event.setBroadcastType(1);
        event.setBroadcastLevel(level);
        event.setStar(level);
        Receiver receiver = new Receiver();
        receiver.setUid(uid);
        List<Receiver> recs = Lists.newArrayList(receiver);
        event.setReceivers(recs);
        event.setTimestamp(System.currentTimeMillis());
        event.setUsedMulti(0);

        TurnoverCommonPlaneComponentAttr attr = getComponentAttr(actId, index);
        onTurnoverCommonPlaneEvent(event, attr);

        return Response.success("调用成功");
    }

    @HdzjEventHandler(value = TurnoverCommonPlaneEvent.class, canRetry = false)
    public void onTurnoverCommonPlaneEvent(TurnoverCommonPlaneEvent event, TurnoverCommonPlaneComponentAttr attr) {
        long propsId = event.getPropsId();
        if (!attr.getGiftId().equals(String.valueOf(propsId))) {
            return;
        }
        log.info("onTurnoverCommonPlaneEvent event:{}", JSON.toJSONString(event));
        long sid = event.getSid();
        long ssid = event.getSsid();
        if(antiPoaching(sid, ssid)) {
            log.info("antiPoaching sid:{}, ssid:{}, event:{}", sid, ssid, JSON.toJSONString(event));
            return;
        }
        //用户若在黑名单的厅送礼,则只发子频道
        boolean inBlackList = false;
        if (attr.getBlackListCmptUseIndex() != 0) {
            BlackListComponentAttr blackListComponentAttr = blackListComponent.getComponentAttr(attr.getActId(), attr.getBlackListCmptUseIndex());
            if (blackListComponentAttr != null && StringUtil.isNotBlank(blackListComponentAttr.getBlackList())) {
                String blackList = blackListComponentAttr.getBlackList();
                String ting = sid + "_" + ssid;
                if (StringUtil.isNotBlank(ting) && blackList.contains(ting)) {
                    log.info("onTurnoverCommonPlaneEvent user in black list ting, memberId:{}, event:{}", event.getSendUid(), JSON.toJSONString(event));
                    inBlackList = true;
                }
            }
        }

        int level = event.getBroadcastLevel();
        AppBannerSvgaConfig svgaConfig = attr.getLevel2svgaConfig().get(level);

        if (svgaConfig == null) {
            log.info("onTurnoverCommonPlaneEvent level2config is null, seq:{}, event:{}", event.getSeqId(), JSON.toJSONString(event));
            return;
        }

        long actId = event.getActId();

        long comboHits = event.getComboHits();
        List<Receiver> receivers = event.getReceivers();
        long count = event.getCount();
        long compereUid = event.getCompereUid();
        int broadcastType = inBlackList ? 1 : event.getBroadcastType();

        List<Long> collect = receivers.stream().map(Receiver::getUid).collect(Collectors.toList());
        Long anchorUid = CollectionUtils.isEmpty(collect) ? event.getCompereUid() : collect.get(0);
        String zjContentLayers = svgaConfig.getZjContentLayers();
        zjContentLayers = contextReplace(zjContentLayers, comboHits, count, event.getSendUid(),
                anchorUid, commonService.getNickName(compereUid, false), attr.getGiftName(), event.getUsedMulti());

        List<JSONObject> contentLayers = JSON.parseArray(zjContentLayers, JSONObject.class);
        String zjImgLayers = svgaConfig.getZjImgLayers();
        List<JSONObject> imgLayers = JSON.parseArray(zjImgLayers, JSONObject.class);
        svgaConfig.setContentLayers(contentLayers);
        svgaConfig.setImgLayers(imgLayers);
        svgaConfig.setZjImgLayers("");
        svgaConfig.setZjContentLayers("");
        svgaConfig.setNameCountLimit(attr.getNameCountLimit());

        Map<Integer, Integer> level2business = attr.getLevel2business();
        ArrayList<Long> uidList = Lists.newArrayList(event.getSendUid(), anchorUid, event.getCompereUid());

        AppBannerEvent appBannerEvent = kafkaService.buildAppBannerEvent(actId, event.getSeqId(), level2business.get(level), broadcastType, sid, ssid,
                attr.getGiftIcon(), uidList, "");
        appBannerEvent.setAppId(event.getAppId());
        appBannerEvent.setChannel(event.getChannel());
        appBannerEvent.setSystemInfo(event.getSystemInfo() + "");
        appBannerEvent.setPlatform(event.getPlatform());
        appBannerEvent.setCompereUid(event.getCompereUid());
        appBannerEvent.setAirType(1);
        appBannerEvent.setUid(event.getSendUid());

        appBannerEvent.setSvgaConfig(svgaConfig);
        if (attr.isMpt4()) {
            //发送mp4
            List<Map<String, String>> layerKeys = new ArrayList<>();
            if(contentLayers!= null && !contentLayers.isEmpty()) {
                for (JSONObject contentLayer : contentLayers) {
                    for (String key : contentLayer.keySet()) {
                        Map<String, String> map = new HashMap<>();
                        map.put(key, contentLayer.getString(key));
                        layerKeys.add(map);
                    }
                }
            }
            if(imgLayers!= null && !imgLayers.isEmpty()) {
                for (JSONObject imgLayer : imgLayers) {
                    for (String key : imgLayer.keySet()) {
                        Map<String, String> map = new HashMap<>();
                        map.put(key, imgLayer.getString(key));
                        layerKeys.add(map);
                    }
                }
            }
            broMp42App(attr.getActId(), event.getSeqId(), attr.getLevel2business().get(level), broadcastType, sid, ssid, uidList,
                    svgaConfig.getSvgaURL(), 999, layerKeys, new ArrayList<>(), event, attr.getExcludeDanmaku());
        } else {
            if(attr.getExcludeDanmaku() != 0) {
                kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
            } else {
                kafkaService.sendAppBannerKafka(appBannerEvent);
            }
            log.info("onTurnoverCommonPlaneEvent sendAppBannerKafka svga done,seq:{} event:{}", event.getSeqId(), JSON.toJSONString(appBannerEvent));
        }
    }

    public void broMp42App(long actId, String seq, int business, int bcType, long sid,
                           long ssid, List<Long> uids, String mp4Url, int broLevel, List<Map<String, String>> layerKeyValues, List<String> filterList,
                           TurnoverCommonPlaneEvent event, int excludeDanmuka) {
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, seq, business,
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setAirType(1);
        appBannerEvent.setAppId(event.getAppId());
        appBannerEvent.setChannel(event.getChannel());
        appBannerEvent.setSystemInfo(event.getSystemInfo() + "");
        appBannerEvent.setPlatform(event.getPlatform());
        appBannerEvent.setCompereUid(event.getCompereUid());
        appBannerEvent.setUid(event.getSendUid());
        appBannerEvent.setUidList(uids);
        appBannerEvent.setContentType(5);
        appBannerEvent.setPushUidlist(uids);
        AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
        appBannerMp4Config.setUrl(mp4Url);
        appBannerMp4Config.setLevel(broLevel);
        appBannerMp4Config.setLayerExtKeyValues(layerKeyValues);
        appBannerEvent.setMp4Config(appBannerMp4Config);
        appBannerEvent.setFilterList(filterList);
        if(excludeDanmuka != 0) {
            kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
        } else {
            kafkaService.sendAppBannerKafka(appBannerEvent);
        }
        log.info("app bro mp4 done seq:{}, event:{}", seq, JSON.toJSONString(appBannerEvent));
    }


    private String contextReplace(String context, long comboHits, long count, long userUid, long
            anchorUid, String compereUid, String giftName, int usedMulti) {
        String sendType = "";
        if (usedMulti == Const.ONE) {
            sendType = "全麦打赏";
        } else if (usedMulti == Const.TOW) {
            sendType = "多人打赏";
        }
        return context.replace("{comboHits}", comboHits + "")
                .replace("{count}", count + "")
                .replace("{userNick}", "{" + userUid + ":n}")
                .replace("{anchorNick}", "{" + anchorUid + ":n}")
                .replace("{giftName}", giftName)
                .replace("{sendType}", sendType)
                .replace("{compereNick}", "{" + compereUid + ":n}");
    }

    public boolean antiPoaching(long sid, long ssid) {
        List<AntiPoachingInfo> list = ftsRoomManagerThriftClient.getAntiPoachingList();
        Set<String> set = new HashSet<>();
        if(list != null) {
            for (AntiPoachingInfo antiPoachingInfo : list) {
                if(antiPoachingInfo.isBillboard) {
                    String ting = antiPoachingInfo.getSid() + "_" + antiPoachingInfo.getSsid();
                    set.add(ting);
                }
            }
            log.info("antiPoachingInfo list:{}", GsonUtil.toJson(list));
            String key = sid+"_"+ssid;
            return set.contains(key);
        } else {
            log.info("antiPoachingInfos is null");
        }
        return false;
    }

}
