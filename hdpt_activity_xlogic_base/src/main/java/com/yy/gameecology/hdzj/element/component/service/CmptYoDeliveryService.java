package com.yy.gameecology.hdzj.element.component.service;

import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.NanoIdUtils;
import com.yy.gameecology.hdzj.element.component.attr.YOMedalComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryAccountDao;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryRecListDao;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryWhiteListDao;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class CmptYoDeliveryService {

    @Autowired
    private CmptYoDeliveryRecListDao cmptYoDeliveryRecListDao;

    @Autowired
    private CmptYoDeliveryAccountDao cmptYoDeliveryAccountDao;

    @Autowired
    private CmptYoDeliveryWhiteListDao cmptYoDeliveryWhiteListDao;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Transactional
    public void sendDelivery(long recUid, long sendUid,
                             long actId, long cmptUseInx, int tp, int cnt, String seq,
                             long currencyTaskId, long currencyPackageId) {
            int recRow = cmptYoDeliveryRecListDao.addRecList(actId, cmptUseInx, recUid, sendUid, tp, 0, seq);
            log.info("addRecList sendUid:{}, recUid:{}, actId:{}, cmptUseInx:{}, tp:{}, row: {}", sendUid, recUid, actId, cmptUseInx, tp, recRow);
            doWelfareMedal(seq, actId, recUid, currencyTaskId, currencyPackageId);
    }

    @Transactional
    public void splitInxAndReward(long uid, int inx, int scene, long actId, long cmptUseInx, int tp, int cnt) {
        cmptYoDeliveryWhiteListDao.updateWhiteList(actId, cmptUseInx, uid, inx, scene);
        cmptYoDeliveryAccountDao.incrAccount(actId, cmptUseInx, uid, tp, cnt);
    }

    private void doWelfareMedal(String seq, long busiid, long uid, long taskId, long packageId) {
        BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(busiid, uid, taskId, 1, packageId, seq);
        log.info("doWelfareMedal with seq:{}, result:{}", seq, welfareResult);

        //todo
        if (welfareResult == null || welfareResult.getCode() != 0) {
            throw new RuntimeException("welfare fail");
        }
    }
}
