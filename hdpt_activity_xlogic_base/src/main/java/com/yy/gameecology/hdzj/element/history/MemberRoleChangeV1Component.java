package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.MemberRoleChangeV1ComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * 已废弃 功能已被 5037 组件替代
 * 淘汰成员角色改变组件
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 * @see MemberRoleChangeComponent
 */
@Component
@Deprecated
public class MemberRoleChangeV1Component extends BaseActComponent<MemberRoleChangeV1ComponentAttr> {


    @Override
    public Long getComponentId() {
        return ComponentId.MEMBER_ROLE_CHANGE;
    }


    /**
     * 榜单结束事件-调整淘汰成员的角色 以达到赛道调整的目的
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void adjustEliminatedMemberRole(PromotTimeEnd event, MemberRoleChangeV1ComponentAttr attr) {
        if (!attr.getRankIds().contains(event.getRankId()) || !attr.getPhaseIds().contains(event.getPhaseId())) {
            return;
        }
        log.info("adjustEliminatedMemberRole start,event:{} attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        long actId = attr.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        RoleType roleType = RoleType.findByValue(attr.getMemberRoleType());
        Assert.notNull(roleType, "config error,roleType is null,memberRoleType=" + attr.getMemberRoleType());
        //查询榜单当前阶段的成员
        List<Rank> curRanks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, "", Integer.MAX_VALUE, Maps.newHashMap());
        Assert.isTrue(!curRanks.isEmpty(), "curRanks is empty");

        Set<String> curMember = curRanks.stream().map(Rank::getMember).collect(Collectors.toSet());

        //上一阶段，阶段必须是连续的
        long prePhaseId = phaseId - 1;
        int reTryCount = attr.getReTryCount();

        //查询榜单上个阶段的成员--可能是合赛，要分散去查上个阶段的成员，默认是当前榜
        List<Long> preRankIds = attr.getRankPhasePreRankListMap().getOrDefault(rankId + "-" + phaseId, Lists.newArrayList(rankId));
        //筛选被淘汰的成员和对应的上一阶段榜单
        Map<String, Long> eliminatedMemberPreRankIdMap = queryEliminatedMemberPreRankIdMap(actId, preRankIds, prePhaseId, curMember);
        Assert.isTrue(!eliminatedMemberPreRankIdMap.isEmpty(), "eliminatedMembers is empty,preRankIds=" + JSON.toJSONString(preRankIds));


        //被淘汰的成员
        List<String> eliminatedMembers = Lists.newArrayList(eliminatedMemberPreRankIdMap.keySet());

        //更新成员身份
        Map<String, Long> eliminatedMemberNewRoleIdMap = updateMemberRole(actId, attr.getCmptUseInx(), eliminatedMembers, roleType, attr.getRoleChangeMap(), reTryCount);
        Assert.isTrue(!eliminatedMemberNewRoleIdMap.isEmpty(), "eliminatedMemberNewRoleIdMap is empty,RoleChangeMap=" + JSON.toJSONString(attr.getRoleChangeMap()));


        //暂停一小会，等中台数据完成更新完成
        SysEvHelper.waiting(3000);

        //转移成员主榜
        Map<String, Long> updateScoreSuccessPreRankIdMap = transferMemberScore(phaseId, eliminatedMemberPreRankIdMap, eliminatedMemberNewRoleIdMap, attr);
        Assert.isTrue(!updateScoreSuccessPreRankIdMap.isEmpty(), "updateScoreSuccessPreRankIdMap is empty");


        //转移成功贡献榜-只有主播转移成功的才会转移贡献榜 updateScoreSuccessPreRankIdMap
        transferMemberContributedScore(phaseId, updateScoreSuccessPreRankIdMap, eliminatedMemberNewRoleIdMap, attr);

    }

    /**
     * 手动触发 -失败干预
     *
     * @param actId
     * @param index
     * @param memberId
     * @param rankId
     * @param phaseId
     * @param opUid
     * @return
     */
    public Response manualAdjust(long actId, long index, String memberId, long rankId, long phaseId, long opUid) {
        MemberRoleChangeV1ComponentAttr attr = getComponentAttr(actId, index);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + index);
        if (opUid <= 0) {
            return Response.fail(500, "未登录");
        }
        if (!ArrayUtils.contains(attr.getAdmins(), opUid + "")) {
            return Response.fail(500, "权限不足");
        }

        RoleType roleType = RoleType.findByValue(attr.getMemberRoleType());
        Assert.notNull(roleType, "config error,roleType is null,memberRoleType=" + attr.getMemberRoleType());

        int reTryCount = attr.getReTryCount();

        List<Long> preRankIds = attr.getRankPhasePreRankListMap().getOrDefault(rankId + "-" + phaseId, Lists.newArrayList(rankId));
        //找到成员在哪个榜单
        List<Pair<String, Long>> memberPreRankIds = preRankIds.stream()
                .map(preRankId -> ImmutablePair.of(memberId, preRankId)).collect(Collectors.toList());
        Map<Pair<String, Long>, Rank> preRankResult = queryPointedMembersRanking(actId, memberPreRankIds, phaseId - 1, Maps.newHashMap());
        Long preRankId = preRankResult.entrySet().stream()
                .filter(item -> item.getValue().getRank() > 0)
                .map(item -> item.getKey().getRight()).findFirst().orElse(0L);

        if (preRankId == 0L) {
            return Response.fail(500, "无法找到原始榜单");
        }

        //更新成员身份
        Map<String, Long> eliminatedMemberNewRoleIdMap = updateMemberRole(actId, attr.getCmptUseInx(), Lists.newArrayList(memberId), roleType, attr.getRoleChangeMap(), reTryCount);
        Assert.isTrue(!eliminatedMemberNewRoleIdMap.isEmpty(), "eliminatedMemberNewRoleIdMap is empty,RoleChangeMap=" + JSON.toJSONString(attr.getRoleChangeMap()));

        //暂停一小会，等中台数据完成更新完成
        SysEvHelper.waiting(3000);

        //转移成员主榜
        Map<String, Long> updateScoreSuccessPreRankIdMap = transferMemberScore(phaseId, Collections.singletonMap(memberId, preRankId), eliminatedMemberNewRoleIdMap, attr);
        if (updateScoreSuccessPreRankIdMap.isEmpty()) {
            return Response.fail(500, "主榜分数挪榜失败");
        }
        //转移成功贡献榜-只有主播转移成功的才会转移贡献榜 updateScoreSuccessPreRankIdMap
        transferMemberContributedScore(phaseId, updateScoreSuccessPreRankIdMap, eliminatedMemberNewRoleIdMap, attr);

        return Response.ok();
    }

    /**
     * 转移成员主榜的分数
     *
     * @param phaseId
     * @param memberPreRankIdMap
     * @param memberNewRoleIdMap
     * @param attr
     * @return
     */
    private Map<String, Long> transferMemberScore(long phaseId, Map<String, Long> memberPreRankIdMap, Map<String, Long> memberNewRoleIdMap, MemberRoleChangeV1ComponentAttr attr) {
        long actId = attr.getActId();
        //读取这一阶段的上一个榜单总榜分数
        Map<String, Rank> phaseTotalRankMap = queryPointedMembersRankingAll(actId, memberPreRankIdMap, phaseId, Maps.newHashMap());
        String item = attr.getUpdatingItem();
        Map<String, Long> updateScoreSuccessPreRankIdMap = Maps.newHashMap(memberPreRankIdMap);
        for (Map.Entry<String, Long> entry : memberNewRoleIdMap.entrySet()) {

            String memberId = entry.getKey();
            Long newRoleId = entry.getValue();
            String seq = makeKey(attr, "memberScoreUpdate-" + phaseId + "-" + memberId);
            Long preRankId = memberPreRankIdMap.get(memberId);
            Rank rank = phaseTotalRankMap.get(memberId);
            long score = Optional.ofNullable(rank).map(Rank::getScore).orElse(0L);
            boolean scoreUpdate = true;
            if (score > 0) {
                Map<Long, String> actors = Collections.singletonMap(newRoleId, memberId);
                Map<String, Long> roleScore = Collections.singletonMap(newRoleId.toString(), score);
                scoreUpdate = updateRanking(actId, attr.getCmptUseInx(), item, actors, roleScore, seq, attr.getReTryCount());
            }
            if (!scoreUpdate) {
                updateScoreSuccessPreRankIdMap.remove(memberId);
                log.error("transferMemberScore score update error,actId:{},cmptUseInx:{},preRankId={},phaseId={} member :{} score:{}"
                        , actId, attr.getCmptUseInx(), preRankId, phaseId, memberId, score);
            } else {
                log.info("transferMemberScore score update info,actId:{},cmptUseInx:{},preRankId={},phaseId={} member :{} score:{}"
                        , actId, attr.getCmptUseInx(), preRankId, phaseId, memberId, score);
            }

        }

        return updateScoreSuccessPreRankIdMap;
    }

    /**
     * 转移成员的被贡献榜单分数
     *
     * @param phaseId
     * @param memberPreRankIdMap
     * @param memberNewRoleIdMap
     * @param attr
     * @return
     */
    private Map<String, Long> transferMemberContributedScore(long phaseId, Map<String, Long> memberPreRankIdMap, Map<String, Long> memberNewRoleIdMap, MemberRoleChangeV1ComponentAttr attr) {
        long actId = attr.getActId();
        long contributeRankIdAdd = attr.getContributeRankIdAdd();
        long contributeMemberRole = attr.getContributeMemberRole();
        String item = attr.getUpdatingItem();
        Map<String, Long> updateScoreSuccessPreRankIdMap = Maps.newHashMap(memberPreRankIdMap);
        //查询残留的贡献榜-并上报
        Map<String, List<Rank>> contributeRanksMap = queryMembersContributeRanking(actId, memberPreRankIdMap, phaseId, contributeRankIdAdd);
        for (Map.Entry<String, List<Rank>> entry : contributeRanksMap.entrySet()) {
            List<Rank> ranks = entry.getValue();
            String memberId = entry.getKey();
            Long preRankId = memberPreRankIdMap.get(memberId);
            if (CollectionUtils.isEmpty(ranks)) {
                log.info("adjustEliminatedMemberRole contribute update info ,ranks is empty.memberId:{} preRankId:{}", memberId, preRankId);
                continue;
            }
            Long newRoleId = memberNewRoleIdMap.get(memberId);
            for (Rank rank : ranks) {
                String playerUid = rank.getMember();
                Long score = rank.getScore();
                String seq = makeKey(attr, "contributeScoreUpdate-" + phaseId + "-" + memberId + "-" + playerUid);
                Map<Long, String> actors = ImmutableMap.of(newRoleId, memberId, contributeMemberRole, playerUid);
                Map<String, Long> roleScore = ImmutableMap.of(contributeMemberRole + "", score);
                boolean scoreUpdate = updateRanking(actId, attr.getCmptUseInx(), item, actors, roleScore, seq, attr.getReTryCount());
                if (!scoreUpdate) {
                    updateScoreSuccessPreRankIdMap.remove(memberId);
                    log.error("transferMemberContributedScore update error,actId:{},cmptUseInx:{},preRankId={},phaseId={} member :{} playerUid:{} score:{}"
                            , actId, attr.getCmptUseInx(), preRankId, phaseId, memberId, playerUid, score);
                } else {
                    log.info("transferMemberContributedScore  update info,actId:{},cmptUseInx:{},preRankId={},phaseId={} member :{} playerUid:{} score:{}"
                            , actId, attr.getCmptUseInx(), preRankId, phaseId, memberId, playerUid, score);
                }
            }
        }
        return updateScoreSuccessPreRankIdMap;
    }

    /**
     * 查询指定成员的榜单阶段分数-不限定晋级资格的key
     *
     * @param actId
     * @param memberRankIdMap
     * @param phaseId
     * @param ext
     * @return
     */
    private Map<String, Rank> queryPointedMembersRankingAll(long actId, Map<String, Long> memberRankIdMap, long phaseId, Map<String, String> ext) {
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (Map.Entry<String, Long> entry : memberRankIdMap.entrySet()) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(entry.getValue());
            rankingRequest.setPhaseId(phaseId);
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr("");
            rankingRequest.setPointedMember(entry.getKey());
            rankingRequest.setExtData(Collections.singletonMap(RankExtParaKey.RANK_TYPE_ALL, ""));
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Maps.newHashMap());
        return ranksList.stream().flatMap(Collection::stream).filter(Objects::nonNull)
                .collect(Collectors.toMap(Rank::getMember, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 批量查询指定成员的分数
     *
     * @param actId
     * @param memberRankIds
     * @param phaseId
     * @param ext
     * @return
     */
    private Map<Pair<String, Long>, Rank> queryPointedMembersRanking(long actId, List<Pair<String, Long>> memberRankIds, long phaseId, Map<String, String> ext) {
        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (Pair<String, Long> memberRankId : memberRankIds) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(memberRankId.getRight());
            rankingRequest.setPhaseId(phaseId);
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr("");
            rankingRequest.setPointedMember(memberRankId.getLeft());
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Maps.newHashMap());
        return IntStream.range(0, memberRankIds.size())
                .boxed()
                .collect(Collectors.toMap(memberRankIds::get, i -> ranksList.get(i).get(0), (k1, k2) -> k1));
    }

    /**
     * 批量查询成员的贡献榜所有数据
     *
     * @param actId
     * @param memberRankIdMap
     * @param phaseId
     * @param contributeRankIdAdd
     * @return
     */
    private Map<String, List<Rank>> queryMembersContributeRanking(long actId, Map<String, Long> memberRankIdMap, long phaseId, long contributeRankIdAdd) {
        Map<String, QueryRankingRequest> contributeQueryRequestMap = Maps.newHashMap();
        //构造批量请求参数
        for (Map.Entry<String, Long> entry : memberRankIdMap.entrySet()) {

            String anchorUid = entry.getKey();
            long contributePreRankId = entry.getValue() + contributeRankIdAdd;
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(contributePreRankId);
            rankingRequest.setPhaseId(phaseId);
            rankingRequest.setRankingCount(Integer.MAX_VALUE);
            rankingRequest.setExtData(Collections.singletonMap(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, anchorUid));
            contributeQueryRequestMap.put(anchorUid, rankingRequest);
        }
        Map<String, BatchRankingItem> contributeRanksMap = hdztRankingThriftClient.queryBatchRanking(contributeQueryRequestMap, Maps.newHashMap());
        return contributeRanksMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, item -> item.getValue().getData()));
    }

    /**
     * 更新成员的角色
     *
     * @param actId
     * @param cmptUseInx
     * @param eliminatedMembers
     * @param roleType
     * @param roleChangeMap
     * @param tryTime
     * @return
     */
    private Map<String, Long> updateMemberRole(long actId, long cmptUseInx, List<String> eliminatedMembers, RoleType roleType, Map<Long, Long> roleChangeMap, int tryTime) {

        //被淘汰成员原来角色
        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfoNocache(actId, (long) roleType.getValue(), eliminatedMembers);
        Map<String, EnrollmentInfo> enrollmentInfoMap = enrollmentInfos.stream().collect(Collectors.toMap(EnrollmentInfo::getMemberId, Function.identity()));

        Map<String, Long> newRoleMap = Maps.newHashMap();
        //查询被淘汰新的身份-声音、视频、天团 修改主播身份
        for (String eliminatedMember : eliminatedMembers) {
            String seq = UUID.randomUUID().toString();
            EnrollmentInfo enrollmentInfo = enrollmentInfoMap.get(eliminatedMember);
            if (enrollmentInfo == null) {
                log.error("updateMemberRole not find enrollmentInfo,actId:{},cmptUseInx:{},member:{} ", actId, cmptUseInx, eliminatedMember);
                continue;
            }
            Long oldRoleId = enrollmentInfo.getDestRoleId();
            Long newRoleId = roleChangeMap.get(oldRoleId);
            //找不到新的角色
            if (newRoleId == null) {
                //不在老的角色，属于新角色
                if (roleChangeMap.values().contains(oldRoleId)) {
                    newRoleMap.put(eliminatedMember, oldRoleId);
                    log.info("updateMemberRole ignore,actId:{},cmptUseInx:{},member:{} oldRoleId：{}", actId, cmptUseInx, eliminatedMember, oldRoleId);
                } else {
                    log.error("updateMemberRole not find newRoleId,actId:{},cmptUseInx:{},member:{} oldRoleId：{}", actId, cmptUseInx, eliminatedMember, oldRoleId);
                }
                continue;
            }
            EnrollmentInfo newEnrollmentInfo = new EnrollmentInfo(enrollmentInfo);
            newEnrollmentInfo.setSrcRoleId(newRoleId);
            newEnrollmentInfo.setDestRoleId(newRoleId);

            String remark = String.format("淘汰成员角色调整,oldRoleId:%s,cmptId:%s,cmptUseInx:%s,ip:%s", oldRoleId, getComponentId(), cmptUseInx, SystemUtil.getIp());
            int updateCount = 0;
            int ret;

            do {
                updateCount++;
                ret = hdztRankingThriftClient.saveEnrollment(actId, newEnrollmentInfo, true, remark, seq);
                if (ret == 0) {
                    break;
                }
                SysEvHelper.waiting(1000);

            } while (updateCount < Math.max(tryTime, 1));

            if (ret == 0) {
                newRoleMap.put(eliminatedMember, newRoleId);
                log.info("updateMemberRole info,actId:{},cmptUseInx:{},member:{} oldRoleId:{},newRoleId:{},updateCount:{}", actId, cmptUseInx, eliminatedMember, oldRoleId, newRoleId, updateCount);
            } else {
                log.error("updateMemberRole error,actId:{},cmptUseInx:{},member:{} oldRoleId:{},newRoleId:{},updateCount:{}", actId, cmptUseInx, eliminatedMember, oldRoleId, newRoleId, updateCount);
            }
        }
        return newRoleMap;
    }

    /**
     * 查找上一阶段被淘汰的成员以及对应的榜单
     *
     * @param actId
     * @param preRankIds
     * @param prePhaseId
     * @param curMember
     * @return
     */
    private Map<String, Long> queryEliminatedMemberPreRankIdMap(long actId, List<Long> preRankIds, long prePhaseId, final Collection<String> curMember) {


        //批量查询上一阶段的成员
        Map<String, QueryRankingRequest> batchQueryRequestMap = Maps.newHashMap();
        for (Long preRankId : preRankIds) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(actId);
            rankingRequest.setRankingId(preRankId);
            rankingRequest.setPhaseId(prePhaseId);
            rankingRequest.setRankingCount(Integer.MAX_VALUE);
            rankingRequest.setExtData(Maps.newHashMap());
            batchQueryRequestMap.put(preRankId.toString(), rankingRequest);
        }
        //查找上一阶段被淘汰的主播和对应的榜单id（需要补分）
        Map<String, BatchRankingItem> preRanksMap = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, Maps.newHashMap());

        //筛选出被淘汰的成员
        Map<String, Long> eliminatedAnchorPreRankIdMap = Maps.newHashMap();
        for (Map.Entry<String, BatchRankingItem> entry : preRanksMap.entrySet()) {
            Long preRankId = Long.parseLong(entry.getKey());
            Map<String, Long> memberPreRankIdMap = entry.getValue().getData().stream()
                    .map(Rank::getMember).filter(member -> !curMember.contains(member))
                    .collect(Collectors.toMap(Function.identity(), (key) -> preRankId));
            log.info("queryEliminatedAnchorPreRankIdMap info，preRankId={} memberPreRankIdMap size ={} member={}"
                    , preRankId, memberPreRankIdMap.keySet().size(), JSON.toJSONString(memberPreRankIdMap.keySet()));
            eliminatedAnchorPreRankIdMap.putAll(memberPreRankIdMap);
        }

        return eliminatedAnchorPreRankIdMap;
    }


    /**
     * @param actId
     * @param cmptUseInx
     * @param item
     * @param actors
     * @param roleScore
     * @param seq
     * @return
     */
    private boolean updateRanking(long actId, long cmptUseInx, String item, Map<Long, String> actors, Map<String, Long> roleScore, String seq, int reTryCount) {

        int i = 0;
        long time = commonService.getNow(actId).getTime();
        do {
            try {
                i++;
                UpdateRankingRequest request = new UpdateRankingRequest();
                request.setBusiId(BusiId.GAME_ECOLOGY.getValue());
                request.setActId(actId);
                request.setSeq("zk-" + seq);
                request.setActors(actors);
                //约战免费礼物
                request.setItemId(item);
                request.setCount(0);
                request.setScore(0L);
                request.setRoleScores(roleScore);
                request.setRankScores(Maps.newHashMap());
                request.setTimestamp(time);

                UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);

                log.info("updateRanking actId:{} cmptUseInx:{} request:{} result:{}",
                        actId, cmptUseInx, JSON.toJSONString(request), JSON.toJSONString(result));
                return true;
            } catch (Exception e) {
                log.error("updateRanking error,actId:{} cmptUseInx:{},item:{},actors:{},roleScore:{}seq:{},time:{},try:{}"
                        , actId, cmptUseInx, item, JSON.toJSONString(actors), JSON.toJSONString(roleScore), seq, time, i, e);
            }

        } while (i < reTryCount);
        return false;
    }
}
