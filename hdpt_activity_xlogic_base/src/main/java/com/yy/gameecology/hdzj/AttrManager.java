package com.yy.gameecology.hdzj;

import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;

/**
 * 功能描述:属性管理器
 * <AUTHOR>
 * @date 2021/4/7 18:25
 */
public class AttrManager {
    protected static final Logger log = LoggerFactory.getLogger(AttrManager.class);

    public static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 功能描述:获取组件请求对象
     * <AUTHOR>
     * @date 2021/4/7 18:26
     */
    public static ComponentRequest getComponentRequest(ActComponent actComponent, long actId, long cmptUseInx) {
        ComponentRequest request = new ComponentRequest();
        ComponentAttr componentAttr = actComponent.getComponentAttr(actId, cmptUseInx);
        request.setComponentAttr(componentAttr);
        return request;
    }

//    /**
//     * 功能描述:替换同名属性 - 注意基础类型只支持  int/long/double/float/boolean
//     * <AUTHOR>
//     * @date 2021/4/7 18:28
//     */
//    public static void adjustAttrValue(Object attr, Map<String, String> map) throws IllegalAccessException, ParseException {
//        Field[] fields = attr.getClass().getDeclaredFields();
//        for(Field field : fields) {
//            String name = field.getName();
//            if(map.containsKey(name)) {
//                field.setAccessible(true);
//                String value = StringUtil.trim(map.get(name));
//                Class<?> type = field.getType();
//                if(type.isPrimitive()) {
//                    if (type == Integer.TYPE) {
//                        field.set(attr, new Integer(value));
//                    } else if (type == Long.TYPE) {
//                        field.set(attr, new Long(value));
//                    } else if (type == Double.TYPE) {
//                        field.set(attr, new Double(value));
//                    } else if (type == Float.TYPE) {
//                        field.set(attr, new Float(value));
//                    } else if(type == Boolean.TYPE) {
//                        field.set(attr, "1".equals(value));
//                    } else if(type == Character.TYPE) {
//                        if(!value.isEmpty()) {
//                            field.set(attr, value.charAt(0));
//                        }
//                    } else {
//                        throw new IllegalAccessException("不支持的基础类型：" + type.getName() + " " + name);
//                    }
//                } else if (type == Boolean.class) {
//                    field.set(attr, "1".equals(value));
//                } else if (type == String.class) {
//                    field.set(attr, value);
//                } else if (type == Character.class) {
//                    if(!value.isEmpty()) {
//                        field.set(attr, value.charAt(0));
//                    }
//                } else if (type == Date.class) {
//                    field.set(attr, dateFormat.parse(value));
//                } else if (Number.class.isAssignableFrom(type)) {
//                    Object object = JSON.parseObject(value, type);
//                    field.set(attr, object);
//                } else {
//                    // TODO::后续优化，只针对含泛型的对象如此处理
//                    String newJson = String.format("{\"%s\":%s}", name, value);
//                    Object object = JSON.parseObject(newJson, attr.getClass());
//                    field.set(attr, field.get(object));
//                }
//            }
//        }
//    }
}
