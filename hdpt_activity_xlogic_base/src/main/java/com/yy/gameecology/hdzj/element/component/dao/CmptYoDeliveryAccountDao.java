package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CmptYoDeliveryAccountDao {

    @Autowired
    private GameecologyDao gameecologyDao;


    private static final String GET_ACCOUNT =
            """
            SELECT * FROM cmpt_yo_delivery_account
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND uid = %s
            AND tp = %s
            """;

    private static final String INCR_ACCOUNT =
            """
            INSERT INTO cmpt_yo_delivery_account
           (act_id, cmpt_use_inx, uid, tp, cnt)
           VALUES (%s, %s, %s, %s, %s)
            on duplicate key update cnt = cnt + %s
            """;

    private static final String LIST_ACCOUNT =
            """
            SELECT uid FROM cmpt_yo_delivery_account
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND tp = %s
            AND cnt > 0
            LIMIT 3000
            """;

    private static final String DECR_ACCOUNT =
            """
            UPDATE cmpt_yo_delivery_account SET cnt = cnt - %s
            WHERE act_id = %s AND cmpt_use_inx = %s AND uid = %s AND tp = %s AND cnt >= %s;
            """;

    private static final String GET_COUNT =
            """
            SELECT COUNT(*) FROM cmpt_yo_delivery_account
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            """;

    public CmptYoDeliveryAccount getCmptYoDeliveryAccount(long actId, long cmptUseInx, long uid, int tp) {
        CmptYoDeliveryAccount account = null;
        try {
            account = gameecologyDao.getJdbcTemplate().queryForObject(
                    String.format(GET_ACCOUNT, actId, cmptUseInx, uid, tp),
                    CmptYoDeliveryAccount.ROW_MAPPER);
        } catch (IncorrectResultSizeDataAccessException e) {
            return null;
        }
        return account;
    }

    public int incrAccount(long actId, long cmptUseInx, long uid, int tp, int cnt) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(INCR_ACCOUNT, actId, cmptUseInx, uid, tp, cnt, cnt));
    }

    public List<Long> listAccount(long actId, long cmptUseInx, int tp) {
        return gameecologyDao.getJdbcTemplate().query(
                String.format(LIST_ACCOUNT, actId, cmptUseInx, tp),
                new SingleColumnRowMapper<>(Long.class));
    }


    public int decrAccount(long actId, long cmptUseInx, long uid, int tp, int cnt) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(DECR_ACCOUNT, cnt, actId, cmptUseInx, uid, tp, cnt));
    }

    public Integer getCount(long actId, long cmptUseInx) {
        return gameecologyDao.getJdbcTemplate().queryForObject(
                String.format(GET_COUNT, actId, cmptUseInx),
                Integer.class);
    }
}
