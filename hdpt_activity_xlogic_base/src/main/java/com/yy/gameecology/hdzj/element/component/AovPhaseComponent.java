package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mms.MmsCmdResult;
import com.yy.gameecology.activity.bean.mms.MmsReportCmdReqVo;
import com.yy.gameecology.activity.bean.mms.MmsReportCmdRspVo;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.service.aov.phase.AovPhaseJobService;
import com.yy.gameecology.activity.service.aov.phase.AovPhaseTeamService;
import com.yy.gameecology.common.consts.ActStatus;
import com.yy.gameecology.common.consts.MmsConst;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseRoundExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhase;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseRound;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeam;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamMember;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.component.attr.AovPhaseComponentAttr;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

@RestController
@RequestMapping("/5115")
@Component
public class AovPhaseComponent extends BaseActComponent<AovPhaseComponentAttr> {

    /**
     * 根据组件配置，生成phase<br/>
     * 1、当前有进行中的phase 自动生成同一个活动ID下的新phase<br/>
     * 2、当前没有进行中的phase 根据组件配置生成新phase
     */
    @NeedRecycle(author = "guanqihua", notRecycle = true)
    @Scheduled(initialDelay = 3000, fixedDelay = 15000)
    public void initPhase() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            ActivityInfoVo actInfo = actInfoService.queryActivityInfo(actId);
            if (!Objects.equals(actInfo.getStatus(), ActStatus.NORMAL)) {
                return;
            }
            AovPhaseComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("initGamePhase:" + actId, 60, () -> {
                Date now = commonService.getNow(actId);
                log.info("trying to init phase with now:{}", now);
                AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
                if (aovPhase != null) {
                    // 报名结束后才生成下一阶段
                    if (aovPhase.getSignupEndTime().after(now)) {
                        return;
                    }

                    AovPhase nextPhase = aovPhaseMapper.selectNextPhase(actId, aovPhase.getId());
                    if (nextPhase != null) {
                        return;
                    }

                    Date time = DateUtils.addMinutes(aovPhase.getEndTime(), 10);

                    Date endTime = new Date(actInfo.getEndTime());
                    if (time.after(endTime)) {
                        return;
                    }

                    // 判断活动挂起（暂停）时间
                    if (attr.getSuspendBeginTime() != null && attr.getSuspendBeginTime().before(time)) {
                        if (attr.getSuspendEndTime() == null || time.before(attr.getSuspendEndTime())) {
                            log.info("initPhase skip suspendBeginTime:{} suspendBeginTime{}", attr.getSuspendBeginTime(), attr.getSuspendEndTime());
                            return;
                        }
                    }

                    // 自动创建的下一期开始报名时间为 零点
                    time = DateUtils.truncate(time, Calendar.DAY_OF_MONTH);

                    aovPhaseJobService.initPhase(attr, time, aovPhase.getActId(), aovPhase.getId());

                    return;
                }

                final Date firstStartTime;
                if (attr.getBeginTime() != null) {
                    firstStartTime = attr.getBeginTime();
                } else {
                    firstStartTime = new Date(actInfo.getBeginTime());
                }

                // 7天内尚未开始，跳过
                if (DateUtils.addDays(now, 7).before(firstStartTime)) {
                    log.info("initPhase empty currentPhase skip now:{} firstStartTime:{}", now, firstStartTime);
                    return;
                }

                // 10分钟后的比赛
                Date phaseStartTime = DateUtils.addMinutes(now, 10);
                Date endTime = new Date(actInfo.getEndTime());
                if (phaseStartTime.after(endTime)) {
                    return;
                }

                // 开始时间必须要大于第一阶段开始时间
                if (phaseStartTime.before(firstStartTime)) {
                    phaseStartTime = firstStartTime;
                }

                // 判断活动挂起（暂停）时间
                if (attr.getSuspendBeginTime() != null && attr.getSuspendBeginTime().before(phaseStartTime)) {
                    if (attr.getSuspendEndTime() == null || phaseStartTime.before(attr.getSuspendEndTime())) {
                        log.info("initPhase skip suspendBeginTime:{} suspendBeginTime{} phaseStartTime:{}", attr.getSuspendBeginTime(), attr.getSuspendEndTime(), phaseStartTime);
                        return;
                    }

                    phaseStartTime = attr.getSuspendEndTime();
                }

                log.info("trying to init empty phase with phaseStartTime:{}", phaseStartTime);

                long prevActId = attr.getPrevActId() > 0 ? attr.getPrevActId() : attr.getActId();
                AovPhase lastPhase = aovPhaseMapper.selectLastPhase(now, prevActId);
                long prevPhaseId = lastPhase == null ? -1L : lastPhase.getId();
                AovPhase nextPhase = aovPhaseMapper.selectNextPhase(prevActId, prevPhaseId);
                if (nextPhase != null) {
                    return;
                }

                aovPhaseJobService.initPhase(attr, phaseStartTime, prevActId, prevPhaseId);
            });
        }
    }

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Autowired
    private AovPhaseJobService aovPhaseJobService;

    @Autowired
    private AovPhaseTeamService aovPhaseTeamService;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    @Autowired
    private AovTeamComponent aovTeamComponent;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Autowired
    private AovPushComponent aovPushComponent;

    private String publicKey = "MIIBuDCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoDgYUAAoGBAP1R1jLPc1kikRwexRvKZhmR01hxFTCYrRaDX8/g+gmQAWWHf0fOrAi0R7dr6BRlT3unfNMgAi8U2+Iet7vpSz1EgG4ZXRc4XSK704jhMV0FPF98OFKFDBWlxJsNnt/MwKiwIA9KHbC89OzJGSap02Mqfa0f8LzMUkP848EZDJkD";

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_GAME_PHASE;
    }

    @RequestMapping("/getSkinConfig")
    public Response<Map<String, Object>> getSkinConfig(long actId, long cmptInx) {
        AovPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }
        List<ActSkin> actSkins = attr.getActSkinConfigs().stream().map(p -> {
            ActSkin skin = new ActSkin();
            skin.setSkinCode(p.getSkinCode());
            skin.setStartTime(p.getStartTime());
            skin.setEndTime(p.getEndTime());
            return skin;
        }).toList();
        Map<String, Object> result = ImmutableMap.of("defaultSkin", attr.getDefaultSkin()
                , "actSkins", actSkins
                , "currentTime", commonService.getNow(actId).getTime()
                , "serverTime", System.currentTimeMillis());
        return Response.success(result);
    }

    @RequestMapping("/testCreatePhase")
    public Response<?> testCreatePhase(long actId, long cmptInx) {
        AovPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        return Response.ok();
    }

    @RequestMapping("/getPhaseInfo")
    public Response<PhaseInfo> getPhaseInfo(@RequestParam(name = "actId") long actId,
                                            @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx) {
        AovPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "活动不存在");
        }

        Date now = commonService.getNow(actId);
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        PhaseInfo phaseInfo = new PhaseInfo();
        phaseInfo.setServerTime(now.getTime());
        boolean noCurrentPhase = false;
        if (phase == null) {
            noCurrentPhase = true;
            phaseInfo.setCancel(true);
            AovPhase prevPhase = aovPhaseMapper.selectLastPhase(now, actId);

            if (prevPhase == null) {
                phaseInfo.setPhaseId(-1);
                phaseInfo.setPrevPhaseId(-1);
                AovPhase nextPhase = aovPhaseMapper.selectNextPhase(actId, -1L);
                if (nextPhase != null) {
                    phaseInfo.setNextSignupTime(nextPhase.getSignupStartTime().getTime());
                }

                return Response.success(phaseInfo);
            }

            phase = prevPhase;
        }

        long uid = getLoginYYUid();

        String matches = phase.getMatchGroupInfo();
        List<AovPhaseJobService.RoundGroup> matchList = JSONObject.parseArray(matches, AovPhaseJobService.RoundGroup.class);
        List<Contest> contestList = new ArrayList<>(matchList.size() + 1);
        // 先添加一个报名
        Contest signupContext = new Contest();
        signupContext.setName("报名");
        signupContext.setStartTime(phase.getSignupStartTime().getTime());
        signupContext.setEndTime(phase.getSignupEndTime().getTime() - DateUtils.MILLIS_PER_SECOND);
        contestList.add(signupContext);
        for (AovPhaseJobService.RoundGroup group : matchList) {
            Contest contest = new Contest();
            contest.setName(group.getName());
            contest.setStartTime(group.getStartTime().getTime());
            contest.setEndTime(group.getEndTime().getTime());
            contestList.add(contest);
        }
        phaseInfo.setContests(contestList);
        phaseInfo.setState(phase.getState());
        phaseInfo.setPhaseId(phase.getId());
        phaseInfo.setAwards(phase.getAwardInfo());
        phaseInfo.setSignStartTime(phase.getSignupStartTime().getTime());
        phaseInfo.setSignEndTime(phase.getSignupEndTime().getTime());
        if(phase.getState() == AovConst.PhaseState.CANCEL) {
            phaseInfo.setCancel(true);
        }

        // 当前无phase则prevPhaseId返回 当前的phaseId
        phaseInfo.setPrevPhaseId(noCurrentPhase ? phase.getId() : phase.getPrevPhaseId());

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRounds(phase.getId());
        AovPhaseRound firstRound = rounds.stream().filter(round -> round.getState() != AovConst.RoundState.INIT).findFirst()
                .orElse(rounds.getFirst());

        phaseInfo.setFirstGameStartTime(firstRound.getStartTime().getTime());

        if (now.after(firstRound.getStartTime())) {
            phaseInfo.setRankActId(actId);
            phaseInfo.setRankPhaseId(phase.getId());
        } else {
            phaseInfo.setRankActId(phase.getPrevActId());
            phaseInfo.setRankPhaseId(phase.getPrevPhaseId());
        }

        if (noCurrentPhase) {
            phaseInfo.setSign(false);
        } else {
            AovPhaseTeamMember member = aovPhaseTeamMemberMapper.selectByUniq(phaseInfo.getPhaseId(), uid);
            phaseInfo.setSign(member != null);
        }

        phaseInfo.setAppointment(aovPushComponent.hasSubscribe(actId, uid, phaseInfo.getPhaseId()));
        AovPhase nextPhase = aovPhaseMapper.selectNextPhase(actId, phase.getId());
        if (nextPhase != null) {
            phaseInfo.setNextSignupTime(nextPhase.getSignupStartTime().getTime());
        }
        return Response.success(phaseInfo);
    }

    /**
     * 判断是否已实名
     * @return
     */
    @RequestMapping("/certify")
    public Response<CertifyRsp> certify() {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        CertifyRsp rsp = new CertifyRsp();
        String idHash = userinfoThriftClient.getIdHash(uid);
        if(!StringUtil.isEmpty(idHash)) {
            rsp.setPass(true);
        }
        return Response.success(rsp);
    }

    @RequestMapping("/mobileBind")
    public Response<CertifyRsp> hasMobileBind() {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        CertifyRsp rsp = new CertifyRsp();
        String mobileHash = userinfoThriftClient.getMobileHash(uid);
        if(!StringUtil.isEmpty(mobileHash)) {
            rsp.setPass(true);
        }
        return Response.success(rsp);
    }

    /**
     * 创建战队
     * @param request
     * @param actId
     * @param cmptInx
     * @param msg
     * @param needApply
     * @param verifyCode
     * @param recordId
     * @param verifyToken
     * @return
     */
    @RequestMapping("/createTeam")
    public Response<?> createTeam(HttpServletRequest request,
                               @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                               @RequestParam(required = false, defaultValue = "") String msg,
                               @RequestParam("needApply")int needApply,
                               @RequestParam(name = "verifyCode", required = false) String verifyCode,
                               @RequestParam(name = "recordId", required = false) String recordId,
                               @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        return aovTeamComponent.createTeam(request, actId, cmptInx, msg, needApply, verifyCode, recordId, verifyToken);
    }

    /**
     * （队长）切换入队审批开关
     * @param actId
     * @param cmptInx
     * @param teamId
     * @return
     */
    @RequestMapping("applySwitch")
    public Response<?> turnApplySwitch(@RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                       @RequestParam(name = "teamId") long teamId) {
        return aovTeamComponent.turnApplySwitch(actId, cmptInx, teamId);
    }

    /**
     * 我的队伍信息
     * @param actId
     * @param cmptInx
     * @return
     */
    @RequestMapping("/myTeamInfo")
    public Response<MyTeamRsp> myTeamInfo(@RequestParam("actId") long actId,
                                          @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                          @RequestParam(name = "phaseId", required = false) Long phaseId) {
        return aovTeamComponent.myTeamInfo(actId, cmptInx, phaseId);
    }

    /**
     * 队伍列表
     * @param actId
     * @param cmptInx
     * @param phaseId
     * @param page
     * @param pageSize
     * @return
     */
    @RequestMapping("/teamList")
    public Response<TeamListRsp> teamList(@RequestParam("actId") long actId,
                                          @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                          @RequestParam(name = "phaseId", required = false) Long phaseId,
                                          @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
                                          @RequestParam(name = "pageSize", required = false, defaultValue = "30") Integer pageSize) {
        return aovTeamComponent.teamList(actId, cmptInx, phaseId, page, pageSize);
    }

    @RequestMapping("kickOutMember")
    public Response<?> kickOutMember(@RequestParam("actId") long actId,
                                     @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                     @RequestParam(name = "teamId") long teamId,
                                     @RequestParam(name = "memberUid") long memberUid) {
        return aovTeamComponent.kickOutMember(actId, cmptInx, teamId, memberUid);
    }

    /**
     * 离开队伍（队员）or 解散队伍（队长）
     * @param actId
     * @param cmptInx
     * @param teamId
     * @return
     */
    @RequestMapping("quitTeam")
    public Response<?> quitTeam(@RequestParam("actId") long actId,
                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                @RequestParam(name = "teamId") long teamId) {
        return aovTeamComponent.quitTeam(actId, cmptInx, teamId);
    }

    @RequestMapping("/inRoom")
    public Response<InRoomRsp> inRoom(@RequestParam("actId") long actId,
                                      @RequestParam("cmptInx") long cmptInx,
                                      @RequestParam("teamId")long teamId) {
        return aovTeamComponent.inRoom(actId, cmptInx, teamId);
    }

    @RequestMapping("/commonCallback")
    public MmsReportCmdRspVo commonCallback(HttpServletRequest request,
                                            @RequestParam("actId") long actId,
                                            String appKey,
                                            String serial,
                                            String cmd,
                                            String reason,
                                            String msg,
                                            @RequestParam(value = "extParUrlEncoder", required = false, defaultValue = "") String extParUrlEncoder,
                                            String sign,
                                            String md5sign,
                                            String status,
                                            String reasonCode) {
        String groups = request.getParameter("groups");
        log.info("commonCallback req, appKey:{}, serial:{}, cmd:{}, reason:{}, msg:{}, extParUrlEncoder:{}, sign:{}, md5sign:{}, status:{}, reasonCode:{}, groups:{}", appKey, serial, cmd, reason, msg, extParUrlEncoder, sign, md5sign, status, reasonCode, groups);
        MmsReportCmdReqVo mmsReportCmdReq = MmsReportCmdReqVo.builder().build();
        mmsReportCmdReq.setAppKey(appKey);
        mmsReportCmdReq.setSerial(serial);
        mmsReportCmdReq.setCmd(cmd);
        mmsReportCmdReq.setReason(reason);
        mmsReportCmdReq.setMsg(msg);
        mmsReportCmdReq.setExtParUrlEncoder(extParUrlEncoder);
        mmsReportCmdReq.setSign(sign);
        mmsReportCmdReq.setStatus(status);
        mmsReportCmdReq.setReasonCode(reasonCode);
        mmsReportCmdReq.setMd5sign(md5sign);
        mmsReportCmdReq.setGroups(groups);
        if (!checkCallbackSign(mmsReportCmdReq)) {
            log.warn("commonCallback checkCallbackSign error mmsReportCmdReq:{}", JSON.toJSONString(mmsReportCmdReq));
            return MmsReportCmdRspVo.SUCCESS;
        }

        //区分队伍宣言和队伍审批回调
        int mmsType = calMmsType(extParUrlEncoder);
        MmsCmdResult result = MmsCmdResult.fromValue(mmsReportCmdReq.getStatus());
        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectById(Long.parseLong(serial));
        if (aovPhaseTeam == null) {
            log.warn("commonCallback process error mmsReportCmdReq:{}", JSON.toJSONString(mmsReportCmdReq));
            return MmsReportCmdRspVo.SUCCESS;
        }
        if (MmsConst.MmsType.AOV_PHASE_TEAM_DESC == mmsType) {
            if (MmsCmdResult.VIOLATION.equals(result)) {
                int rs = aovPhaseTeamService.clearAuditDeclaration(AovConst.AovTeamMsgAuditState.REJECT, Long.parseLong(serial), AovConst.AovTeamMsgAuditState.INIT);
                aovPushComponent.sendRejectedNotice(actId, aovPhaseTeam.getCreator());
            } else if (MmsCmdResult.ACCESS.equals(result)) {
                int rs = aovPhaseTeamService.acceptDeclaration(AovConst.AovTeamMsgAuditState.PASS, Long.parseLong(serial), AovConst.AovTeamMsgAuditState.INIT);
            }
        } else if (MmsConst.MmsType.AOV_PHASE_TEAM_NAME == mmsType) {
            long actIdPara = calActId(extParUrlEncoder);
            var attr = getUniqueComponentAttr(actIdPara);
            var pushAttr = aovPushComponent.getUniqueComponentAttr(actIdPara);
            aovPhaseTeamService.doTeamNameAudit(attr, pushAttr, aovPhaseTeam.getCreator(), Convert.toLong(serial), result);
        }
        return MmsReportCmdRspVo.SUCCESS;
    }

    private int calMmsType(String para) {
        if (!StringUtil.isJson(para)) {
            return MmsConst.MmsType.AOV_PHASE_TEAM_DESC;
        }
        JSONObject extObject = JSON.parseObject(para);
        return Convert.toInt(extObject.getInteger("mmsType"), MmsConst.MmsType.AOV_PHASE_TEAM_DESC);
    }

    private long calActId(String para) {
        if (StringUtil.isBlank(para) || !StringUtil.isJson(para)) {
            return 0;
        }
        JSONObject extObject = JSON.parseObject(para);
        return Convert.toInt(extObject.getInteger("actId"), 0);
    }

    public boolean checkCallbackSign(MmsReportCmdReqVo mmsReportCmdReq) {
        boolean isOk = false;
        String appKey = mmsReportCmdReq.getAppKey();
        String serial = mmsReportCmdReq.getSerial();
        String cmd = mmsReportCmdReq.getCmd();
        String reason = mmsReportCmdReq.getReason();
        String msg = mmsReportCmdReq.getMsg();
        String extPar = mmsReportCmdReq.getExtParUrlEncoder() != null ? mmsReportCmdReq.getExtParUrlEncoder() : "";
        String sign = mmsReportCmdReq.getSign();
        try {
            String inf = String.format("%s%s%s%s%s%s", appKey, serial, cmd, reason, msg, extPar);
            isOk = verify(inf.getBytes(), publicKey, sign);
            log.info("checkCallbackSign inf=" + inf + ",isOk=" + isOk);
        } catch (Exception e) {
            log.error("checkCallbackSign error ,mmsReportCmdReq:{},error:{}" ,JSON.toJSONString(mmsReportCmdReq), e.getMessage(), e);
        }
        return isOk;
    }

    public static boolean verify(byte[] data, String publicKey, String sign) throws Exception {
        String algorithm = "DSA";
        byte[] keyBytes = Base64.decodeBase64(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(keyFactory.getAlgorithm());
        signature.initVerify(pubKey);
        signature.update(data);
        return signature.verify(Base64.decodeBase64(sign));
    }

    /**
     * (申请)加入战队
     * @param request
     * @param actId
     * @param cmptInx
     * @param msg
     * @param teamId
     * @param verifyCode
     * @param recordId
     * @param verifyToken
     * @return
     */
    @RequestMapping("/joinTeam")
    public Response<?> joinTeam(HttpServletRequest request,
                                @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                @RequestParam(required = false, defaultValue = "") String msg,
                                @RequestParam("teamId")long teamId,
                                @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                @RequestParam(name = "recordId", required = false) String recordId,
                                @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        return aovTeamComponent.joinTeam(request, actId, cmptInx, msg, teamId, verifyCode, recordId, verifyToken);
    }

    /**
     * 获取入队申请列表
     * @param actId
     * @param cmptInx
     * @return
     */
    @RequestMapping("/applyList")
    public Response<ApplyListRsp> applyList(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx) {
        return aovTeamComponent.applyList(actId, cmptInx);
    }

    /**
     * 队长审批入队申请
     * @param actId
     * @param cmptInx
     * @param applyId
     * @param apply 0 - 拒绝， 1 - 通过
     * @return
     */
    @RequestMapping("/apply")
    public Response<?> apply(@RequestParam("actId") long actId,
                             @RequestParam("cmptInx") long cmptInx,
                             @RequestParam("applyId") long applyId,
                             @RequestParam("apply") int apply) {
        return aovTeamComponent.apply(actId, cmptInx, applyId, apply);
    }

    /**
     * 修改战队宣言
     * @param actId
     * @param cmptInx
     * @param msg
     * @return
     */
    @RequestMapping("/changeDeclaration")
    public Response<?> changeDeclaration(@RequestParam("actId") long actId,
                                      @RequestParam("cmptInx") long cmptInx,
                                      @RequestParam("msg")String msg) {
        return aovTeamComponent.changeDeclaration(actId, cmptInx, msg);
    }

    /**
     * 修改战队名称
     * @param actId
     * @param cmptInx
     * @param teamId
     * @param teamName
     * @return
     */
    @RequestMapping("changeTeamName")
    public Response<?> changeTeamName(@RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                      @RequestParam(name = "teamId") long teamId,
                                      @RequestParam(name = "teamName") String teamName) {
        return aovTeamComponent.changeTeamName(actId, cmptInx, teamId, teamName);
    }

    @Data
    public static class PhaseInfo {
        private long phaseId;
        private long prevPhaseId;
        /**
         * 当前用户是否已报名
         */
        private boolean sign;
        private boolean cancel;
        private List<Contest> contests;
        private String awards;
        private long signStartTime;
        private long signEndTime;
        private long firstGameStartTime;
        private long serverTime;
        private boolean appointment;
        private long nextSignupTime;
        private long rankActId;
        private long rankPhaseId;
        private int state;
    }

    @Data
    public static class Contest {
        private long startTime;
        private long endTime;
        private String name;
    }

    @Data
    public static class CertifyRsp {
        private boolean pass;
    }

    @Data
    public static class ApplyListRsp {
        private boolean captain;

        private List<ApplyInfo> applyInfos;
    }

    @Data
    public static class ApplyInfo {
        private long uid;
        private long applyId;
        private String teamName;
        private String name;
        private String avatar;
        private String applyTime;
        private Integer tp;
        private String msg;
    }

    @Data
    public static class Teamate {
        private boolean live; // 是否在app
        private String avatar;
        private String name;
        private Long uid;
    }

    @Data
    public static class Team {
        private Long teamId;
        private String name;
        private String teamNameAudit;
        private String declaration;
        private List<Teamate> teamates;
        private Long sid;
        private Long ssid;
        private Integer state = AovConst.PhaseTeamShowState.SIGNING;
        private Integer tp;
        private String declarationAudit;
        private boolean inRoom;
        private Integer teamNameAuditState;
        private Integer memberCnt;
        private Integer memberLimit;
    }

    @Data
    public static class MyTeamRsp {
        private boolean canJoin;

        /**
         * 是否展示红点<br/>
         * 当前用户已在队伍中且是队长：有待查看的入队申请 红点
         * 当前用户未在队伍中：有待查看被审批完成的入队申请 红点
         */
        private boolean redDot;

        /**
         * 当前用户已在队伍中且是队长
         */
        private boolean captain;

        private String teamLeadGroupQr;

        private String teamLeadGroupId;

        private String teammatesGroupQr;

        private String declarationDefault;

        private String wxNum;

        /**
         * 当前队伍信息
         */
        private Team currentTeam;

        /**
         * 当前队伍信息下面的感动文案
         */
        private List<Notify> notifyList;

        /**
         * 创建队伍，拉起半窗的时候展示当前用户信息
         */
        private MyInfo myInfo;

    }

    @Data
    public static class TeamListRsp {
        /**
         * 队伍列表，当前用户还没加入队伍时，返回队伍未满人的队伍列表
         */
        private List<Team> teamList;

    }

    @Data
    public static class Notify{
        private String text;

        private boolean award;

        private int aovPhaseTeamShowState;

        public Notify() {
        }

        public Notify(String text, boolean award) {
            this.text = text;
            this.award = award;
        }
    }

    @Data
    public static class MyInfo{
        private String avatar;

        private String name;
    }

    @Data
    public static class InRoomRsp {
        private boolean inRoom;
    }

    @Data
    public static class ActSkin {
        @ComponentAttrField(labelText = "开始时段", remark = "时间戳，毫秒")
        private long startTime;

        @ComponentAttrField(labelText = "结束时段", remark = "时间戳，毫秒")
        private long endTime;

        @ComponentAttrField(labelText = "皮肤编码")
        private String skinCode;
    }

}
