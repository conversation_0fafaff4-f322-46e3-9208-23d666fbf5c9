package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-11 15:48
 **/
@Data
public class WzryTaskComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "营收appid")
    private int turnoverAppId;
    @ComponentAttrField(labelText = "营收门票货币类型")
    private int piaoCurrentType;

    @ComponentAttrField(labelText = "门票奖池ID")
    private long piaoTaskId;
    @ComponentAttrField(labelText = "门票奖包ID")
    private long piaoPackageId;

    @ComponentAttrField(labelText = "领取门票风控策略")
    private String riskStrategyKey;

    @ComponentAttrField(labelText = "新用户天限")
    private Integer newUserDay;

    @ComponentAttrField(labelText = "签到Id")
    private int signInId;
    @ComponentAttrField(labelText = "签到周期")
    private int period;
    @ComponentAttrField(labelText = "签到奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "天数"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励门票数")
            })
    private Map<Integer, Integer> signInAward;
    @ComponentAttrField(labelText = "签到新用户奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "天数"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励门票数")
            })
    private Map<Integer, Integer> newUserSignInAward;


    @ComponentAttrField(labelText = "任务配置配置", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TaskConfig.class)
    })
    private List<TaskConfig> taskConfigList = Collections.emptyList();

    @ComponentAttrField(labelText = "榜单任务源配置", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = HdztTaskSource.class)
    })
    private List<HdztTaskSource> taskSourceList = Collections.emptyList();


    @Data
    public static class TaskConfig {
        @ComponentAttrField(labelText = "类型", dropDownSourceBeanClass = TaskTypeDownSource.class)
        private Integer type;
        @ComponentAttrField(labelText = "周期类型")
        private Integer periodType;
        @ComponentAttrField(labelText = "周期内完成次数")
        private long goal;
        @ComponentAttrField(labelText = "奖励门票数量")
        private int award;
        @ComponentAttrField(labelText = "新用户奖励门票数量")
        private int newUserAward;
        @ComponentAttrField(labelText = "奖励门票详情说明")
        private String taskDesc;
        @ComponentAttrField(labelText = "任务图标")
        private String icon;
        @ComponentAttrField(labelText = "任务名称")
        private String name;
        @ComponentAttrField(labelText = "是否展示在任务列表")
        private boolean show;
    }

    @Data
    public static class HdztTaskSource {
        @ComponentAttrField(labelText = "任务类型", dropDownSourceBeanClass = TaskTypeDownSource.class)
        private Integer type;
        @ComponentAttrField(labelText = "任务榜单")
        private Long rankId;
        @ComponentAttrField(labelText = "任务阶段")
        private Long phaseId;
    }


    public static class TaskTypeDownSource implements DropDownSource {
        @Override
        public List<DropDownVo> listDropDown() {

            return Arrays.stream(TaskType.values())
                    .map(i -> new DropDownVo(String.valueOf(i.code), String.valueOf(i.name)))
                    .toList();
        }
    }

    @AllArgsConstructor
    @Getter
    public static enum TaskType {
        GAME_TEAM(1, "开黑"),
        WZRY_GAME(2, "完成赏金赛"),
        LONGIN(3, "登录");

        private int code;
        private String name;
    }


}
