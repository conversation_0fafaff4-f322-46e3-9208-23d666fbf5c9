package com.yy.gameecology.hdzj.bean.aov;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AovGameInfo {

    public static final int PREVIEW = -1;

    public static final int GAMING = 0;

    public static final int CLOSED = 1;

    protected AovGameTeamInfo current;

    protected AovGameTeamInfo opponent;

    protected long roundId;

    protected int roundNum;

    protected String roundName;

    protected long gameId;

    protected int bo;

    protected int curBo;

    protected int state;

    protected Date startTime;
}
