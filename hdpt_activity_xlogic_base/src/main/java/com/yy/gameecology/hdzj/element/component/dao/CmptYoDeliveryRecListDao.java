package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryRecList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public class CmptYoDeliveryRecListDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    private static final String GET_REC_LIST =
            """
            SELECT * FROM cmpt_yo_delivery_rec_list
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND uid = %s
            AND tp = %s
            ORDER BY ctime DESC
            """;

    private static final String GET_REC_LIST_V2 =
            """
            SELECT * FROM cmpt_yo_delivery_rec_list
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND send_uid = %s
            AND tp = %s
            ORDER BY ctime DESC
            """;

    private static final String GET_REC_LIST_BY_ID =
            """
            SELECT * FROM cmpt_yo_delivery_rec_list
            WHERE id = %s
            """;

    private static final String ADD_REC_LIST =
            """
            INSERT INTO cmpt_yo_delivery_rec_list
            (act_id, cmpt_use_inx, uid, send_uid, tp, state, seq)
             VALUES (%s, %s, %s, %s, %s, %s, '%s');
            """;

    private static final String UPDATE_REC_LIST =
            """
            UPDATE cmpt_yo_delivery_rec_list SET state = %s, package_id = %s WHERE id = %s;
            """;

    public List<CmptYoDeliveryRecList> getRecList(long actId, long cmptUseInx, long uid, int tp) {
        return gameecologyDao.getJdbcTemplate().query(
                String.format(GET_REC_LIST, actId, cmptUseInx, uid, tp),
                CmptYoDeliveryRecList.ROW_MAPPER);
    }

    public int addRecList(long actId, long cmptUseInx, long uid, long sendUid, int tp, int state, String seq) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(ADD_REC_LIST, actId, cmptUseInx, uid, sendUid, tp, state, seq));
    }

    public int updateRecList(long id, int state, long packageId) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(UPDATE_REC_LIST, state, packageId, id));
    }

    public CmptYoDeliveryRecList getRecListById(long id) {
        try {
            return gameecologyDao.getJdbcTemplate().queryForObject(
                    String.format(GET_REC_LIST_BY_ID, id),
                    CmptYoDeliveryRecList.ROW_MAPPER);
        } catch (Exception e) {
            return null;
        }
    }

    public List<CmptYoDeliveryRecList> getRecListV2(long actId, long cmptUseInx, long sendUid, int tp) {
        return gameecologyDao.getJdbcTemplate().query(
                String.format(GET_REC_LIST_V2, actId, cmptUseInx, sendUid, tp),
                CmptYoDeliveryRecList.ROW_MAPPER);
    }
}
