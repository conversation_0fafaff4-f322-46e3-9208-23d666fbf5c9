package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryWhiteList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public class CmptYoDeliveryWhiteListDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    private static final String GET_WHITE_LIST =
            """
            SELECT * FROM cmpt_yo_delivery_white_list
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND uid = %s
            """;

    private static final String UPDATE_WHITE_LIST =
            """
            UPDATE cmpt_yo_delivery_white_list
            SET inx = %s, scene = %s, new_user = %s, hdid = '%s', first_login_time = %s
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND uid = %s
            """;

    private static final String UPDATE_WHITE_LIST_V2 =
            """
            UPDATE cmpt_yo_delivery_white_list
            SET inx = %s, scene = %s, new_user = %s, hdid = '%s', first_login_time = %s
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND uid = %s
            AND first_login_time = 0
            """;

    private static final String GET_WHITE_LIST_UIDS =
            """
            SELECT uid FROM cmpt_yo_delivery_white_list
            WHERE act_id = %s
            AND cmpt_use_inx = %s
            AND inx = %s
            AND first_login_time = 0
            """;

    public CmptYoDeliveryWhiteList getCmptYoDeliveryWhiteList(long actId, long cmptUseInx, long uid) {
        BeanPropertyRowMapper<CmptYoDeliveryWhiteList> mapper = new BeanPropertyRowMapper<CmptYoDeliveryWhiteList>(CmptYoDeliveryWhiteList.class);
        mapper.setPrimitivesDefaultedForNullValue(false);
        CmptYoDeliveryWhiteList cmptYoDeliveryWhiteList = null;
        try {
            cmptYoDeliveryWhiteList = gameecologyDao.getJdbcTemplate().queryForObject(String.format(GET_WHITE_LIST, actId, cmptUseInx, uid), mapper);
        } catch (IncorrectResultSizeDataAccessException e) {
            return null;
        }
        return cmptYoDeliveryWhiteList;
    }

    public CmptYoDeliveryWhiteList getCmptYoDeliveryWhiteList(long actId, long cmptUseInx, String hdId) {
        CmptYoDeliveryWhiteList where = new CmptYoDeliveryWhiteList();
        where.setHdid(hdId);
        where.setActId(actId);
        where.setCmptUseInx(cmptUseInx);
        return gameecologyDao.selectOne(CmptYoDeliveryWhiteList.class, where, null);
    }

    public int updateWhiteList(long actId, long cmptUseInx, long uid, int inx, int scene, int newUser, String hdid, long firstLoginTime) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(UPDATE_WHITE_LIST, inx, scene, newUser, hdid, firstLoginTime, actId, cmptUseInx, uid));
    }

    public int updateWhiteListV2(long actId, long cmptUseInx, long uid, int inx, int scene, int newUser, String hdid, long firstLoginTime) {
        return gameecologyDao.getJdbcTemplate().update(
                String.format(UPDATE_WHITE_LIST_V2, inx, scene, newUser, hdid, firstLoginTime, actId, cmptUseInx, uid));
    }

    public int updateWhiteList(long actId, long cmptUseInx, long uid, int inx, int scene) {
        return updateWhiteList(actId, cmptUseInx, uid, inx, scene, 0, "", 0L);
    }

    public List<Long> getWhiteListUids(long actId, long cmptUseInx, int inx) {
        return gameecologyDao.getJdbcTemplate().query(
                String.format(GET_WHITE_LIST_UIDS, actId, cmptUseInx, inx),
                new org.springframework.jdbc.core.SingleColumnRowMapper<>(Long.class));
    }
}
