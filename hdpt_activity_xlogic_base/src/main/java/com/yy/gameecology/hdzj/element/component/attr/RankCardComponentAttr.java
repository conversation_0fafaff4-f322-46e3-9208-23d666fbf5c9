package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.RankAwardConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:榜单加成组件
 * Add Remark:该组件的定制程度相当高,很难复用 20220524 by wangdonghong
 *
 * @createBy 曾文帜
 * @create 2021-04-22 10:50
 **/
public class RankCardComponentAttr extends ComponentAttr {

    /**
     * 奖励配置
     * <p>
     * key 奖励来源榜单阶段   key 榜单id,阶段id
     */
    @ComponentAttrField(labelText = "奖励配置", remark = "榜单&阶段：配置实例为：2,10 表示榜单id为2 阶段id为10,针对该阶段的结果进行配置"
            , subFields = {
            @SubField(fieldName = "key1", type = String.class, labelText = "榜单&阶段")
            , @SubField(fieldName = "value", type = List.class)
            , @SubField(fieldName = "listValue", type = RankAwardConfig.class)
    })
    private Map<String, List<RankAwardConfig>> rankAwardConfig = Maps.newHashMap();

    /**
     * 加分榜单的特定礼物id
     */
    @ComponentAttrField(labelText = "累榜礼物id")
    private String rankAwardItem = "";

    /**
     * 可使用积分卡的开始时间,null代表不限制
     */
    @ComponentAttrField(labelText = "可使用开始时间")
    private Date useCardStartTime = null;

    /**
     * 可使用积分卡的结束时间,null代表不限制
     */
    @ComponentAttrField(labelText = "可使用结束时间")
    private Date useCardEndTime = null;

    /**
     * 业务id，默认陪玩
     **/
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 900;

    /**
     * 加分的角色id，默认陪玩公会
     **/
    @ComponentAttrField(labelText = "加分的角色id")
    private long roleId = 90004;

    /**
     * 卡的所属角色，默认公会
     **/
    @ComponentAttrField(labelText = "所属角色类型")
    private int roleType = 400;

    @ComponentAttrField(labelText = "扩展属性",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "属性Key"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "属性值")
            })
    private Map<String, Long> extJson = Collections.EMPTY_MAP;

    public Map<String, Long> getExtJson() {
        return extJson;
    }

    public void setExtJson(Map<String, Long> extJson) {
        this.extJson = extJson;
    }

    /**
     * ow不参与公会管理，指定其他人来管理
     * 默认是查询当前登录uid对应的公会,只能使用这些公会下的荣耀卡
     * 暂时不考虑一个人能被指定来管理多个公会的情况
     **/
    @ComponentAttrField(labelText = "uid与公会映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "用户id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "公会id(运营公会)")
            })
    private Map<Long, Long> uid2Sid = Maps.newHashMap();

    public Map<Long, Long> getUid2Sid() {
        return uid2Sid;
    }

    public void setUid2Sid(Map<Long, Long> uid2Sid) {
        this.uid2Sid = uid2Sid;
    }

    /**
     * key为团长id，value为对应的团id
     **/
    @ComponentAttrField(labelText = "团长与团id映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "团长id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "团id")
            })
    private Map<Long, Integer> tuanMap = Maps.newHashMap();

    public Map<Long, Integer> getTuanMap() {
        return tuanMap;
    }

    public void setTuanMap(Map<Long, Integer> tuanMap) {
        this.tuanMap = tuanMap;
    }

    public int getRoleType() {
        return roleType;
    }

    public void setRoleType(int roleType) {
        this.roleType = roleType;
    }

    public long getRoleId() {
        return roleId;
    }

    public void setRoleId(long roleId) {
        this.roleId = roleId;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public Map<String, List<RankAwardConfig>> getRankAwardConfig() {
        return rankAwardConfig;
    }

    public void setRankAwardConfig(Map<String, List<RankAwardConfig>> rankAwardConfig) {
        this.rankAwardConfig = rankAwardConfig;
    }

    public Date getUseCardStartTime() {
        return useCardStartTime;
    }

    public void setUseCardStartTime(Date useCardStartTime) {
        this.useCardStartTime = useCardStartTime;
    }

    public Date getUseCardEndTime() {
        return useCardEndTime;
    }

    public void setUseCardEndTime(Date useCardEndTime) {
        this.useCardEndTime = useCardEndTime;
    }

    public String getRankAwardItem() {
        return rankAwardItem;
    }

    public void setRankAwardItem(String rankAwardItem) {
        this.rankAwardItem = rankAwardItem;
    }
}
