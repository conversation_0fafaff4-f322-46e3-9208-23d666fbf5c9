package com.yy.gameecology.hdzj.bean;

import lombok.Data;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-11-22 18:21
 **/
@Data
public class AwardResult {
    private long packageId;
    private long num;
    private String awardName;
    private String unit;
    private String awardIcon;

    public AwardResult() {}

    public AwardResult(AwardResult awardResult) {
        packageId = awardResult.getPackageId();
        num = awardResult.getNum();
        awardName = awardResult.getAwardName();
        unit = awardResult.getUnit();
        awardIcon = awardResult.getAwardIcon();
    }
}
