package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5146Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class MeetGiftDao {

    private static final String INSERT_IGNORE_SQL = "insert ignore into cmpt_5146_record (act_id, uid, hdid, sid, ssid, create_time) values (?, ?, ?, ?, ?, ?)";

    @Autowired
    private GameecologyDao gameecologyDao;

    public List<Cmpt5146Record> selectByUidOrHdid(long actId, long uid, String hdid) {
        Cmpt5146Record me = new Cmpt5146Record();
        me.setActId(actId);
        return gameecologyDao.select(Cmpt5146Record.class, me, " and (uid = " + uid + " or hdid = '" + hdid + "')");
    }

    public int insertIgnore(Cmpt5146Record record) {
        return gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE_SQL, record.getActId(), record.getUid(), record.getHdid(), record.getSid(), record.getSsid(), record.getCreateTime());
    }
}
