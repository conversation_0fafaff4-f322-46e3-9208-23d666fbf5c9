package com.yy.gameecology.activity.bean.rankroleinfo;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/9/17 0:54
 * @Modified:
 */
public class RoleItem implements RoleKey{

    private String name;
    private String avatarInfo;
    private Map<String,String> viewExt = Maps.newHashMap();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAvatarInfo() {
        return avatarInfo;
    }

    public void setAvatarInfo(String avatarInfo) {
        this.avatarInfo = avatarInfo;
    }

    public Map<String, String> getViewExt() {
        return viewExt;
    }

    public void setViewExt(Map<String, String> viewExt) {
        this.viewExt = viewExt;
    }

    @Override
    public void setKey(String key) {
        setName(key);
    }

    @Override
    public String getKey() {
       return getName();
    }
}
