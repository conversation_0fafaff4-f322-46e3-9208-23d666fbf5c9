package com.yy.gameecology.activity.worker.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.AwardRecordVo;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @create 2021-01-13 15:40
 **/
@RestController
@CrossOrigin(allowCredentials = "true", originPatterns = {"yy.com", "*.yy.com"})
@RequestMapping("/hdzt_award")
public class HdztAwardController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private HdztAwardService hdztAwardService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CacheService cacheService;

    /**
     * 获取我的抽奖记录 / guoliping
     */
    @GetMapping("getMyAwardRecords")
    public Response<List<AwardRecordVo>> getMyAwardRecords(HttpServletRequest req, HttpServletResponse resp) {
        Clock clock = new Clock();
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        long busiId = Convert.toLong(req.getParameter("busiId"), -1);
        boolean useCache = Convert.toInt(req.getParameter("useCache"), 0) == 1;
        String taskIds = StringUtil.trim(req.getParameter("taskIds"));
        String packageIds = StringUtil.trim(req.getParameter("packageIds"));
        if(taskIds.contains("2c")) {
            try {
                taskIds = URLDecoder.decode(taskIds, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("decode error");
            }
        }
        try {
            List<Integer> taskIdList = taskIds.isEmpty() ? null : Arrays.stream(taskIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<Long> packageIdList = packageIds.isEmpty() ? null : Arrays.stream(packageIds.split(",")).map(Convert::toLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(taskIdList)) {
                String[] reqTaskIds = req.getParameterValues("taskId");
                if (reqTaskIds != null && reqTaskIds.length > 0) {
                    taskIdList = Arrays.stream(reqTaskIds).map(StringUtils::trim).filter(StringUtils::isNumeric).map(Integer::parseInt).toList();
                }

                if (CollectionUtils.isEmpty(taskIdList)) {
                    return Response.fail(-1, "无效抽奖任务信息：" + taskIds);
                }
            }

            List<AwardRecordVo> list = hdztAwardService.getMyAwardRecords(uid, busiId, taskIdList, packageIdList, useCache);
            if (CollectionUtils.isEmpty(list)) {
                return Response.success(list);
            }

            List<Long> cpUids = list.stream().map(AwardRecordVo::getCpUid).filter(cpUid -> cpUid > 0).distinct().toList();
            if (CollectionUtils.isEmpty(cpUids)) {
                return Response.success(list);
            }
            List<Long> uids = new ArrayList<>();
            uids.addAll(cpUids);
            uids.add(uid);
            Map<String, Map<String, MultiNickItem>> nickExtUsers = Maps.newHashMap();
            Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), nickExtUsers, false, Template.all.getCode());
            Map<Long, UserInfoVo> cpUserInfos = userInfoService.getUserInfo(cpUids, Template.unknown);
            if (MapUtils.isEmpty(cpUserInfos)) {
                return Response.success(list);
            }
            List<AwardRecordVo> listT = new ArrayList<>();
            for (AwardRecordVo item : list) {
                UserInfoVo cpUserInfo = userInfoVoMap.get(item.getCpUid());
                UserInfoVo userInfoVo = userInfoVoMap.get(item.getUid());
                if(userInfoVo != null) {
                    item.setNick(userInfoVo.getNick());
                    item.setAvatar(userInfoVo.getAvatarUrl());
                }
                if (cpUserInfo != null) {
                    item.setCpNick(cpUserInfo.getNick());
                    item.setCpAvatar(cpUserInfo.getAvatarUrl());
                    item.setNickExtUsers(MapUtils.isEmpty(nickExtUsers) ?
                            org.apache.commons.lang3.StringUtils.EMPTY : JSON.toJSONString(nickExtUsers));
                }
                if(!StringUtil.isEmpty(item.getCpMember())) {
                    String[] strs = item.getCpMember().split("\\|");
                    if(Convert.toLong(strs[0]) != uid) {
                        String anchorNick = item.getNick();
                        String anchorAvatar = item.getAvatar();
                        item.setNick(item.getCpNick());
                        item.setAvatar(item.getCpAvatar());
                        item.setCpNick(anchorNick);
                        item.setCpAvatar(anchorAvatar);
                        item.setUid(item.getCpUid());
                        item.setCpUid(uid);
                    }
                }
                item.setGiftNum(item.getAmount());
                if(!StringUtil.isEmpty(item.getViewExtjson())) {
                    if (StringUtil.isJson(item.getViewExtjson())) {
                        JSONObject jsonObject = JSON.parseObject(item.getViewExtjson());
                        if(jsonObject.containsKey("giftNum")) {
                            item.setGiftNum(jsonObject.getLong("giftNum"));
                        }
                    }
                }
                listT.add(item);
            }
            return Response.success(listT);
        } catch (Exception e) {
            log.error("getMyAwardRecords exception@uid:{}, busiId:{}, useCache:{}, taskIds:{}, err:{} {}",
                    uid, busiId, useCache, taskIds, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 获取全服抽奖记录
     * @param utemplate 用户信息 1===交友 9999===yy
     * @return
     */
    @GetMapping("getAwardRecords")
    public Response<List<AwardRecordVo>> getAwardRecords(HttpServletRequest req, HttpServletResponse resp, @Deprecated Integer utemplate) {
        Clock clock = new Clock();
        long busiId = Convert.toLong(req.getParameter("busiId"), -1);
        boolean useCache = Convert.toInt(req.getParameter("useCache"), 0) == 1;
        long actId = Convert.toLong(req.getParameter("actId"), -1);
        if (busiId <= 0 || actId <= 0) {
            return Response.fail(-1, "参数异常");
        }
        //默认YY昵称
//        int template = Convert.toInt(utemplate, Template.gamebaby.getCode());
        String taskIds = cacheService.getActAttrValue(actId, "getAwardRecords.taskIds", "");
        String packageIds = cacheService.getActAttrValue(actId, "getAwardRecords.packageIds", "");
        boolean needJump = Convert.toBoolean(cacheService.getActAttrValue(actId, "getAwardRecords.needJump", "false"));
        try {
            List<Integer> taskIdList = StringUtils.isEmpty(taskIds) ? null : Arrays.stream(taskIds.split(",")).map(StringUtils::trim).filter(StringUtils::isNumeric).map(Integer::parseInt).toList();
            List<Long> packageIdList = StringUtils.isEmpty(packageIds) ? null : Arrays.stream(packageIds.split(",")).map(StringUtils::trim).filter(StringUtils::isNumeric).map(Long::parseLong).toList();

            String[] reqTaskIds = req.getParameterValues("taskId");
            if (reqTaskIds != null && reqTaskIds.length > 0) {
                taskIdList = Arrays.stream(reqTaskIds).map(StringUtils::trim).filter(StringUtils::isNumeric).map(Integer::parseInt).toList();
            }

            String[] reqPackageIds = req.getParameterValues("packageId");
            if (reqPackageIds != null && reqPackageIds.length > 0) {
                packageIdList = Arrays.stream(reqPackageIds).map(StringUtils::trim).filter(StringUtils::isNumeric).map(Long::parseLong).toList();
            }

            if (CollectionUtils.isEmpty(taskIdList)) {
                return Response.fail(-1, "无效抽奖任务信息：" + taskIds);
            }

            List<AwardRecordVo> list = hdztAwardService.getAwardRecords(busiId, taskIdList, packageIdList, useCache);
            if (CollectionUtils.isEmpty(list)) {
                return Response.success(list);
            }

            Set<Long> uids = new HashSet<>(list.size() * 2);
            for (AwardRecordVo item : list) {
                uids.add(item.getUid());
                if (item.getCpUid() > 0) {
                    uids.add(item.getCpUid());
                }
            }

            Map<String, Map<String, MultiNickItem>> nickExtUsers = Maps.newHashMap();
            Map<Long, UserInfoVo> userBaseInfoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), nickExtUsers, false, Template.all.getCode());

            List<AwardRecordVo> listT = new ArrayList<>();
            Set<String> cpUniqSeqSet = new HashSet<>();
            for (AwardRecordVo item : list) {
                UserInfoVo userBaseInfo = userBaseInfoMap.get(item.getUid());
                if (userBaseInfo != null) {
                    item.setNick(userBaseInfo.getNick());
                    item.setAvatar(userBaseInfo.getAvatarUrl());
                } else {
                    item.setNick("神秘用户");
                }
                userBaseInfo = userBaseInfoMap.get(item.getCpUid());
                if (userBaseInfo != null) {
                    item.setCpNick(userBaseInfo.getNick());
                    item.setCpAvatar(userBaseInfo.getAvatarUrl());
                }
                if(!StringUtil.isEmpty(item.getCpMember())) {
                    String[] strs = item.getCpMember().split("\\|");
                    if(Convert.toLong(strs[0]) != item.getUid()) {
                        String anchorNick = item.getNick();
                        String anchorAvatar = item.getAvatar();
                        long uid = item.getUid();
                        item.setNick(item.getCpNick());
                        item.setAvatar(item.getCpAvatar());
                        item.setCpNick(anchorNick);
                        item.setCpAvatar(anchorAvatar);
                        item.setUid(item.getCpUid());
                        item.setCpUid(uid);
                    }
                    Map<String, Map<String, MultiNickItem>> nickExtUsersT = Maps.newHashMap();
                    nickExtUsersT.put(item.getUid()+"", nickExtUsers.get(item.getUid()+""));
                    nickExtUsersT.put(item.getCpUid()+"", nickExtUsers.get(item.getCpUid()+""));
                    item.setNickExtUsers(MapUtils.isEmpty(nickExtUsersT) ?
                            org.apache.commons.lang3.StringUtils.EMPTY : JSON.toJSONString(nickExtUsersT));
                    if(needJump) {
                        ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(item.getCpUid());
                        if(channelInfoVo != null) {
                            item.setSid(channelInfoVo.getSid());
                            item.setSsid(channelInfoVo.getSsid());
                        }
                    }
                    if(StringUtils.isNotEmpty(item.getCpUniqSeq())) {
                        if (cpUniqSeqSet.contains(item.getCpUniqSeq())) {
                            continue;
                        }
                        cpUniqSeqSet.add(item.getCpUniqSeq());
                    }
                }
                item.setGiftNum(item.getAmount());
                if(!StringUtil.isEmpty(item.getViewExtjson())) {
                    if (StringUtil.isJson(item.getViewExtjson())) {
                        JSONObject jsonObject = JSON.parseObject(item.getViewExtjson());
                        if(jsonObject.containsKey("giftNum")) {
                            item.setGiftNum(jsonObject.getLong("giftNum"));
                        }
                    }
                }
                listT.add(item);
            }
            return Response.success(listT);
        } catch (Exception e) {
            log.error("getAwardRecords exception@busiId:{}, useCache:{}, taskIds:{}, err:{} {}",
                    busiId, useCache, taskIds, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 获取轮播抽奖记录 / guoliping
     */
    @GetMapping("getRollAwardRecords")
    public Response getRollAwardRecords(HttpServletRequest req, HttpServletResponse resp) {
        Clock clock = new Clock();
        long actId = Convert.toLong(req.getParameter("actId"), -1);
        long busiId = Convert.toLong(req.getParameter("busiId"), -1);
        boolean useCache = Convert.toInt(req.getParameter("useCache"), 1) == 1;
        int count = Math.max(1, Convert.toInt(req.getParameter("count"), 5));
        try {
            if (actId == -1) {
                return Response.fail(-1, "无效活动标识：" + actId);
            }
            if (busiId == -1) {
                return Response.fail(-1, "无效业务标识：" + busiId);
            }
            List<JSONObject> list = hdztAwardService.getRollAwardRecords(actId, busiId, useCache);
            if (list.size() > count) {
                list = list.subList(0, count);
            }

            // 昵称模糊化处理
            for (JSONObject jo : list) {
                String nick = jo.getString("nick");
                if (nick != null) {
                    jo.put("nick", dim(nick));
                }
            }

            return Response.success(list);
        } catch (Exception e) {
            log.error("getRollAwardRecords exception@actId:{}, busiId:{}, useCache:{}, err:{} {}",
                    actId, busiId, useCache, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    private String dim(String source) {
        int len = source == null ? 0 : source.length();
        final long two = 2;
        if (len <= two) {
            return source;
        }
        return (len == 3 ? "*" : "**") + source.substring(2);
    }
}
