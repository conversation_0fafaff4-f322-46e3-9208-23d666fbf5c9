package com.yy.gameecology.activity.bean.bigdata;


import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: CXZ
 * @Desciption: 活动报表 -大数据
 * @Date: 2021/4/9 16:28
 * @Modified:
 */
public class ActivityReport {

    /**
     * 记录标识，可用于数据防重，若不在意可填""，唯一性由数据上报者自己保证
     **/
    @JSONField(name = "seq", ordinal = 1)
    private String seq = "";
    /**
     * 活动标识
     **/
    @JSONField(name = "act_id", ordinal = 2)
    private Long actId;
    /**
     * 榜单标识，为act_id下的榜单ID 或者其它含义，0：通用，其它值随活动而定
     **/
    @JSONField(name = "rank_id", ordinal = 3)
    private Integer rankId = 0;
    /**
     * 业务标识，0：通用，其它值随活动而定
     *
     * @see com.yy.thrift.hdztranking.BusiId
     **/
    @JSONField(name = "busi_id", ordinal = 4)
    private Integer busiId = 0;
    /**
     * 报表类型，用于区分一个活动下的不同报表，0：生产环境，1：非生产环境
     **/
    @JSONField(name = "report_type", ordinal = 5)
    private Integer reportType;
    /**
     * 参与者标识
     **/
    @JSONField(name = "actor", ordinal = 6)
    private String actor;
    /**
     * 参与者类型，1：主持（主播），2：普通用户，3：频道长号，4：子频道号, 其它值待定
     * <p>
     * RoleType 是一个大类,有时候不能满足业务需求,需要用到细分类别
     *
     * @see com.yy.thrift.hdztranking.RoleType
     **/
    @JSONField(name = "actor_type", ordinal = 7)
    private Integer actorType;
    /**
     * 分值
     **/
    @JSONField(name = "score", ordinal = 8)
    private Long score;
    /**
     * 分值类型
     *
     * @see BigDataScoreType
     **/
    @JSONField(name = "score_type", ordinal = 9)
    private Integer scoreType;
    /**
     * 业务时间，大部分情况下和 report_time 相同,不能null
     **/
    @JSONField(name = "busi_time", ordinal = 10)
    private Long busiTime;
    /**
     * 上报或生成的时间 ，不能null
     **/
    @JSONField(name = "report_time", ordinal = 11)
    private Long reportTime;
    /**
     * int扩展字段
     **/
    @JSONField(name = "ext_int", ordinal = 12)
    private Integer extInt = 0;
    /**
     * Long扩展字段
     **/
    @JSONField(name = "ext_Long", ordinal = 13)
    private Long extLong = 0L;
    /**
     * string扩展字段
     */
    @JSONField(name = "ext_string", ordinal = 14)
    private String extString = "";

    public ActivityReport() {
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Integer getRankId() {
        return rankId;
    }

    public void setRankId(Integer rankId) {
        this.rankId = rankId;
    }

    public Integer getBusiId() {
        return busiId;
    }

    public void setBusiId(Integer busiId) {
        this.busiId = busiId;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    public Integer getActorType() {
        return actorType;
    }

    public void setActorType(Integer actorType) {
        this.actorType = actorType;
    }

    public Long getScore() {
        return score;
    }

    public void setScore(Long score) {
        this.score = score;
    }

    public Integer getScoreType() {
        return scoreType;
    }

    public void setScoreType(Integer scoreType) {
        this.scoreType = scoreType;
    }

    public Long getBusiTime() {
        return busiTime;
    }

    public void setBusiTime(Long busiTime) {
        this.busiTime = busiTime;
    }

    public Long getReportTime() {
        return reportTime;
    }

    public void setReportTime(Long reportTime) {
        this.reportTime = reportTime;
    }

    public Integer getExtInt() {
        return extInt;
    }

    public void setExtInt(Integer extInt) {
        this.extInt = extInt;
    }

    public Long getExtLong() {
        return extLong;
    }

    public void setExtLong(Long extLong) {
        this.extLong = extLong;
    }

    public String getExtString() {
        return extString;
    }

    public void setExtString(String extString) {
        this.extString = extString;
    }

    @Override
    public String toString() {
        //海度的时间戳格式要对应 yyyy-MM-dd HH:mm:ss
        List<Object> values = Lists.newArrayList(seq, actId, rankId, busiId, reportType, actor, actorType, score, scoreType,
                DateUtil.getPattenStrFromTime(busiTime, DateUtil.DEFAULT_PATTERN), DateUtil.getPattenStrFromTime(reportTime, DateUtil.DEFAULT_PATTERN), extInt, extLong, extString);
        return StringUtils.join(values, "\t");

    }

}
