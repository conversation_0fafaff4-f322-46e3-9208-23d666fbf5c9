package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.bean.rank.GuildRankItem;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * desc:榜单构造
 *
 * @createBy 曾文帜
 * @create 2020-08-20 14:41
 **/
@Service
public class HdztRankGenItemService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private HdztRankConfigService hdztRankConfigService;


    @Autowired
    private WebdbThriftClient webdbThriftClient;

    /**
     * 生成公会类榜单成员信息
     */
    public List<GuildRankItem> genGuildRankItems(RankingInfo rankingInfo, List<RankItem> rankBaseItems) {

        List<GuildRankItem> result = Lists.newArrayList();

        Map<String, RankItem> rankBaseItemMap = Maps.newHashMap();

        List<Long> allMembers = Lists.newArrayList();

        //---构造请求参数 第一层key dataSource 第二次key groupId
        Map<Integer, Map<String, List<String>>> dsMemberIdMap = Maps.newHashMap();
        for (RankItem rankItem : rankBaseItems) {
            rankBaseItemMap.put(rankItem.getMember(), rankItem);
            allMembers.add(Convert.toLong(rankItem.getMember(), 0));

            String groupId = rankItem.getGroupId();
            int dataSource = hdztRankConfigService.getRankDataSource(rankingInfo, groupId);

            Map<String, List<String>> dataSourceMemberIds = dsMemberIdMap.getOrDefault(dataSource, Maps.newHashMap());
            List<String> memberIds = dataSourceMemberIds.getOrDefault(groupId, Lists.newArrayList());
            memberIds.add(rankItem.getMember());
            dataSourceMemberIds.put(groupId, memberIds);
            dsMemberIdMap.put(dataSource, dataSourceMemberIds);
        }

        Map<Long, WebdbChannelInfo> channelInfos = webdbThriftClient.batchGetChannelInfo(allMembers);

        for (int dataSource : dsMemberIdMap.keySet()) {
            ActSupportService actSupportClientService = ActSupportService.getInstance(dataSource);
            //自定义数据源
            if (actSupportClientService != null) {

                Map<String, Map<String, MemberItemInfo>> memberInfoMap
                        = actSupportClientService.queryMember(rankingInfo.getActId(), rankingInfo.getRankingId(), dsMemberIdMap.get(dataSource));
                List<GuildRankItem> customRankItem = buildGuildCustomInfo(rankingInfo, memberInfoMap, rankBaseItemMap, channelInfos);
                if (CollectionUtils.isNotEmpty(customRankItem)) {
                    result.addAll(customRankItem);
                }
            }
            //没定义业务数据源，则用基础的
            else {
                Map<String, List<String>> groupMember = dsMemberIdMap.get(dataSource);
                for (String group : groupMember.keySet()) {
                    List<String> memberIds = groupMember.get(group);
                    for (String memberId : memberIds) {
                        RankItem baseInfoItem = rankBaseItemMap.get(memberId);
                        Long sid = Convert.toLong(baseInfoItem.getMember(), 0);

                        WebdbChannelInfo channelInfo = channelInfos.get(sid);
                        GuildRankItem rankItem = buildGuildBaseInfo(baseInfoItem, channelInfo);
                        result.add(rankItem);
                    }
                }
            }
        }

        return result;
    }


    public List<GuildRankItem> buildGuildCustomInfo(RankingInfo rankingInfo,
                                                    Map<String, Map<String, MemberItemInfo>> memberInfoMap,
                                                    Map<String, RankItem> rankBaseItemMap,
                                                    Map<Long, WebdbChannelInfo> channelInfos) {

        List<GuildRankItem> result = Lists.newArrayList();

        for (String groupId : memberInfoMap.keySet()) {
            boolean replaceBaseField = hdztRankConfigService.isReplaceBaseField(rankingInfo, groupId);
            Map<String, MemberItemInfo> memberItemInfos = memberInfoMap.get(groupId);
            for (String memberId : memberItemInfos.keySet()) {
                RankItem baseInfoItem = rankBaseItemMap.get(memberId);
                Long sid = Convert.toLong(baseInfoItem.getMember(), 0);

                WebdbChannelInfo channelInfo = channelInfos.get(sid);
                GuildRankItem rankItem = buildGuildBaseInfo(baseInfoItem, channelInfo);

                MemberItemInfo memberInfo = memberItemInfos.get(memberId);
                //透传给前端的扩展信息
                if (memberInfo != null) {
                    rankItem.setViewExt(memberInfo.getViewExt());
                }
                //配置了覆盖基础信息字段
                if (replaceBaseField && memberInfo != null) {
                    rankItem.setSid(Convert.toLong(memberInfo.getBaseFieldMemberId(), 0));
                    rankItem.setName(memberInfo.getBaseFieldMemberName());
                    rankItem.setAvatarInfo(memberInfo.getBaseFieldMemberUrl());
                    if (memberInfo.getExt() != null && memberInfo.getExt().containsKey("baseFieldAsId")) {
                        rankItem.setAsid(Convert.toLong(memberInfo.getExt().get("baseFieldAsId")));
                    }
                }

                result.add(rankItem);
            }
        }

        return result;
    }

    /**
     * 榜单元素，非自定义数据源
     *
     * @param baseInfoItem 榜单基础元素
     * @param channelInfo  频道数据
     * @return 公会榜单信息
     */
    public GuildRankItem buildGuildBaseInfo(RankItem baseInfoItem, WebdbChannelInfo channelInfo) {
        Long sid = Convert.toLong(baseInfoItem.getMember(), 0);
        GuildRankItem rankItem = new GuildRankItem();
        Long score = baseInfoItem.getScore();
        rankItem.setValue(score);
        rankItem.setRank(baseInfoItem.getRank());
        rankItem.setSid(sid);

        //频道基础信息
        if (channelInfo != null) {
            rankItem.setAsid(Convert.toLong(channelInfo.getAsid(), sid));
            rankItem.setName(channelInfo.getName());
            rankItem.setAvatarInfo(WebdbUtils.getLogo(channelInfo));
        }
        return rankItem;
    }
}
