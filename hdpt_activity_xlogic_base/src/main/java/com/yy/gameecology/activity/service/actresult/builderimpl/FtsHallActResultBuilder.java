package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.bean.FtsRoomMgrInfoVo;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.rankroleinfo.SubGuildRoleItem;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.rolebuilder.impl.SubGuildRoleBuilder;
import com.yy.gameecology.activity.service.HdztRankGenRoleService;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.java.webdb.WebdbChannelInfo;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("ftsHallActResultBuilder")
public class FtsHallActResultBuilder extends BuilderBase implements ActResultMemberLoader {

    @Autowired
    protected FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, WebdbSubChannelInfo> subChannels = webdbServiceClient.batchGetSubChannelInfo(memberIds);
        List<Long> sids = memberIds.stream().map(FtsHallActResultBuilder::getSid).collect(Collectors.toList());
        Map<Long, WebdbChannelInfo> channelInfoMap = webdbThriftClient.batchGetChannelInfo(sids);

        Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(memberIds);
        // boss后台配置的推荐图,key:sid_ssid
        Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(memberIds,
                HdztRankGenRoleService.changeToBusinessType(BusiId.MAKE_FRIEND.getValue()));
        Map<String, MemberInfo> memberInfoMap = new HashMap<>(memberIds.size());
        Map<String, FtsRoomMgrInfoVo> roomMgrInfoMap = ftsRoomManagerThriftClient.ftsBatchGetRoomMgrInfoByCh(new ArrayList<>(memberIds));
        SubGuildRoleBuilder rankBuilder = new SubGuildRoleBuilder();
        Set<String> memberIdSet = new HashSet<>();
        for (String memberId : memberIds) {
            memberIdSet.add(memberId);
        }
        Map<String, SubGuildRoleItem> subGuildRoleItemMap = rankBuilder.buildRankByYy(memberIdSet);
        Map<Long, ChannelBaseInfo> channelBaseInfoMap = commonService.getChannelInfos(List.copyOf(sids), false);
        for (String memberId : memberIds) {
            WebdbSubChannelInfo subChannelInfo = subChannels.get(memberId);
            MemberInfo memberInfo = new MemberInfo();
            long sid = getSid(memberId);
            WebdbChannelInfo channelInfo = channelInfoMap.get(sid);
            if (channelInfo != null) {
                memberInfo.setLogo(WebdbUtils.getLogo(channelInfo));
                memberInfo.setAsid(channelInfo.getAsid());
            }

            if (subChannelInfo != null) {
                String subChannelName = subChannelInfo.getName();
                if (StringUtils.isNotEmpty(subChannelName)) {
                    memberInfo.setName(subChannelName);
                }
            }

            String mgrPic = roleMemberPicMap.get(memberId);
            String recommendPic = recommendPicMap.get(memberId);
            if (StringUtils.isNotEmpty(recommendPic)) {
                memberInfo.setLogo(recommendPic);
            }

            if (StringUtils.isNotEmpty(mgrPic)) {
                memberInfo.setLogo(mgrPic);
            }
            if(roomMgrInfoMap!=null && roomMgrInfoMap.containsKey(memberId) && StringUtils.isNotEmpty(roomMgrInfoMap.get(memberId).getName())) {
                memberInfo.setName(roomMgrInfoMap.get(memberId).getName());
            }
            if(subGuildRoleItemMap.containsKey(memberId) && StringUtils.isEmpty(memberInfo.getName())) {
                SubGuildRoleItem subGuildRoleItem = subGuildRoleItemMap.get(memberId);
                memberInfo.setName(subGuildRoleItem.getName());
            }
            if (channelInfo != null && StringUtils.isEmpty(memberInfo.getName())) {
                memberInfo.setName(channelInfo.getName());
            }

            memberInfoMap.put(memberId, memberInfo);
        }

        return ImmutableMap.of(builderKey(type, roleType), memberInfoMap);
    }

    public static long getSid(String memberId) {
        return Convert.toLong(StringUtils.split(memberId, StringUtil.UNDERSCORE)[0]);
    }
}
