package com.yy.gameecology.activity.client.thrift;

import cn.hutool.core.convert.Convert;
import com.yy.gameecology.activity.client.yrpc.PubgGameGatewayClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.saibao.*;
import com.yy.zhuiya.game.gateway.gen.pb.GameGateway;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-19 14:46
 **/
@Component
public class SaiBaoClient {
    private static final Logger log = LoggerFactory.getLogger(SaiBaoClient.class);

    @Reference(protocol = "nthrift_selector_compact", owner = "${saibaoService_client_s2sname}", registry = "consumer-reg", timeout = 3000, parameters = {"threads", "10"})
    public GameGatewayProvider.Iface proxy = null;

    @Autowired
    private PubgGameGatewayClient pubgGameGatewayClient;

    public GameGatewayProvider.Iface getProxy() {
        return proxy;
    }

    /**
     * 创建赛宝游戏房间
     */
    public CreateRoomVO createRoom(String name, int battleMode, int bo, boolean autoFail) {
        CreateRoomReq req = new CreateRoomReq();
        req.setBattleMode(Convert.toShort(battleMode));
        req.setName(name);
        req.setGame(Game.WZRY);
        req.setBo(bo);
        req.setAutoFail(autoFail);

        String configName = Const.GEPM.getParamValue(GeParamName.SB_ROOM_NAME, "");
        if (StringUtil.isNotBlank(configName)) {
            log.info("use config name:{}", configName);
            req.setName(configName);
        }

        try {
            var result = getProxy().createRoom(req);
            if (result == null || result.getData() == null || result.getCode() != 0) {
                log.error("createRoom error,name:{},battleMode:{},result:{}", name, battleMode, result);
                throw new RuntimeException("createRoom error");
            }
            log.info("createRoom ok,name:{},battleMode:{},result:{}", name, battleMode, result);
            return result.getData();
        } catch (Exception e) {
            log.error("createRoom error,name:{},battleMode:{},e:{}", name, battleMode, e.getMessage(), e);
            throw new RuntimeException("createRoom error");
        }
    }

    /**
     * 加入房间
     */
    public JoinRoomVO joinRoom(String roomId, long userId, int seatId, int camp) {
        return joinRoom(roomId, userId, seatId, camp, 1, 1, 1);
    }

    /**
     * 组装跳转赛宝房间的url
     */
    public static String buildRoomUrl(String url, String childid, String roomParameters, long uid) {
        String targetUrl = url + childid + "?" + roomParameters;
        //腾讯新给的url特征
        final String newUrlTag = "pvpesport.next.user";
        if (url.contains(newUrlTag)) {
            targetUrl = url + "?childid=" + childid + "&" + roomParameters;
        }

        log.info("buildRoomUrl,url:{},childid:{},roomParameters:{},uid:{},targetUrl:{}", url, childid, roomParameters, uid, targetUrl);
        return targetUrl;
    }

    /**
     *  获取赛宝房间连接
     * @param roomId 必须参赛
     * @param userId uid
     * @param seatId 座位号，1~5，可传0，则不指定座位号
     * @param camp 所属阵营，1or2
     * @param disableSeat 是否不能换位置
     * @param disableCancelReady 是否不能取消准备（准备后，取消按钮置灰）
     * @param autoReady 是否自动准备（进房间后自动准备，取消准备后也自动上座准备）
     * @return
     */
    public JoinRoomVO joinRoom(String roomId, long userId, int seatId, int camp, int disableSeat, int disableCancelReady, int autoReady) {
        JoinRoomReq req = new JoinRoomReq();
        req.setRoomId(roomId);
        req.setUserId(userId);
        req.setSeatId(seatId);
        req.setCamp(camp);
        req.setDisableSeat(disableSeat);
        req.setDisableCancelReady(disableCancelReady);
        req.setAutoReady(autoReady);

        try {
            final int maxRetries = 2;
            final long retryDelay = 100;
            var result = RetryTool.executeWithRetry(() -> {
                var joinResult = getProxy().joinRoom(req);
                if (joinResult == null || joinResult.getData() == null || joinResult.getCode() != 0) {
                    log.warn("joinRoom error,roomId:{},userId:{},seatId:{},camp:{},result:{}", roomId, userId, seatId, camp, joinResult);
                    throw new RuntimeException("joinRoom error");
                }
                return joinResult;

            }, maxRetries, retryDelay);

            log.info("joinRoom,roomId:{},userId:{},seatId:{},camp:{},result:{}", roomId, userId, seatId, camp, result);
            return result.getData();
        } catch (Exception e) {
            log.error("joinRoom error,roomId:{},userId:{},seatId:{},camp:{},e:{}", roomId, userId, seatId, camp, e, e);
            throw new RuntimeException("joinRoom error");
        }
    }

    /**
     * 查询赛宝房间id
     */
    public QueryRoomVO queryRoom(String roomId, long userId) {
        QueryRoomReq req = new QueryRoomReq();
        req.setRoomId(roomId);
        req.setUserId(userId);

        try {
            final int maxRetries = 2;
            final long retryDelay = 500;
            var result = RetryTool.executeWithRetry(() -> {
                var gameResult = getProxy().queryRoom(req);
                //如果接口失败，一定要抛出异常，阻断后续结算流程，否则会错误结算，错误关闭赛事！！！
                if (gameResult == null || gameResult.getCode() != 0) {
                    log.warn("queryRoom error try retry,roomId:{},userId:{},result:{}", roomId, userId, gameResult);
                    throw new RuntimeException("queryRoom error");
                }
                return gameResult;
            }, maxRetries, retryDelay);

            log.info("queryRoom roomId:{},uid:{},result:{}", roomId, userId, result);
            return result.getData();
        } catch (Exception e) {
            log.error("queryRoom error,roomId:{},userId:{},result:{}", roomId, userId, e, e);
            throw new RuntimeException("queryRoom error:" + e.getMessage());
        }
    }

    /**
     * 查询赛事结果
     * 如果要用到返回的uid，必须用 com.yy.gameecology.activity.service.wzry.GameGatewayService#queryGameResultTryFixUid(long, java.lang.String, long) 方法
     */
    public RoomGameResultVO queryGameResult(String roomId, long userId) {
        QueryRoomGameResultReq req = new QueryRoomGameResultReq();
        req.setRoomId(roomId);

        try {

            final int maxRetries = 2;
            final long retryDelay = 500;
            var result = RetryTool.executeWithRetry(() -> {
                var gameResult = getProxy().queryGameResult(req);
                //如果接口失败，一定要抛出异常，阻断后续结算流程，否则会错误结算，错误关闭赛事！！！
                if (gameResult == null || gameResult.getCode() != 0) {
                    log.warn("queryGameResult error try retry,roomId:{},userId:{},result:{}", roomId, userId, gameResult);
                    throw new RuntimeException("queryGameResult error");
                }
                return gameResult;
            }, maxRetries, retryDelay);

            log.info("queryGameResult uid:{},roomId:{},result:{}", userId, roomId, result);
            return result.getData();
        } catch (Exception e) {
            log.error("queryGameResult error,roomId:{},userId:{},result:{}", roomId, e, e);
            throw new RuntimeException("queryGameResult error:" + e.getMessage());
        }
    }


    public boolean closeRoom(String roomId) {
        QueryRoomGameResultReq req = new QueryRoomGameResultReq();
        req.setRoomId(roomId);
        try {
            var result = getProxy().closeRoom(roomId);
            if (result == null) {
                log.error("closeRoom error,roomId:{},result:{}", roomId, result);
                throw new RuntimeException("closeRoom error");
            }

            log.info("closeRoom roomId:{},result:{}", roomId, result);
            return result.getCode() == 0;
        } catch (Exception e) {
            log.error("closeRoom error,roomId:{},userId:{},result:{}", roomId, e, e);
            throw new RuntimeException("closeRoom error:" + e.getMessage());
        }
    }

    /**
     * 如果赛宝有关联大账号，用小号参赛，赛宝返回的uid是大号
     * 如果要用到返回的uid,必须用 com.yy.gameecology.activity.service.wzry.GameGatewayService#queryRoomInfoTryFixUid(long, java.lang.String, long) 方法
     *
     *
     */
    public QuickMatchRoomInfoRsp queryRoomInfo(String roomId, long userId) {
        QueryRoomReq req = new QueryRoomReq();
        req.setRoomId(roomId);
        req.setUserId(userId);
        try {
            QuickMatchRoomInfoRsp rsp = getProxy().QueryRoomInfo(req);
            if (rsp == null || rsp.getCode() != 0) {
                log.error("queryRoomInfo fail rsp:{}", rsp);
            }

            return rsp;
        } catch (Exception e) {
            log.error("queryRoomInfo fail", e);
        }

        return null;
    }

    public List<BattleResultVO> queryMatchResult(String roomId) {
        QueryRoomGameResultReq req = new QueryRoomGameResultReq(roomId);
        try {
            BattleResultRsp rsp = getProxy().queryMatchResult(req);
            if (rsp == null || rsp.getCode() != 0) {
                log.error("queryMatchResult fail rsp:{}", rsp);
                return Collections.emptyList();
            }

            return rsp.getBattleResults();
        } catch (Exception e) {
            log.error("queryMatchResult fail", e);
        }

        return Collections.emptyList();
    }

    public String queryBattleLiveInfo(String childId) {
        try {
            GetBattleLiveInfoRsp rsp = getProxy().queryBattleLiveInfo(childId);
            if (rsp == null || rsp.getCode() != 0) {
                log.error("queryMatchResult fail rsp:{}", rsp);
                return "";
            }
            rsp.liveInfo.getLiveBroadcastFlv();
        } catch (Exception e) {
            log.error("queryBattleLiveInfo fail", e);
        }
        return "";
    }

    public String queryLiveRoomInfo(String roomId) {
        try {
            var rsp = getProxy().queryLiveRoomInfo(roomId);
            if (rsp == null || rsp.getCode() != 0) {
                log.warn("queryLiveRoomInfo fail rsp:{}", rsp);
                return null;
            }

            return rsp.getLiveRoomUrl();
        } catch (Exception e) {
            log.error("queryLiveRoomInfo fail", e);
        }

        return null;
    }

    public boolean isPassportBind(long uid) {
        GameGateway.GetPassportBindRsp.PassportBindVO result = pubgGameGatewayClient.getBindInfo(uid, GameGateway.Game.GAME_KINGS_GLORY);
        boolean bind = result != null && result.getBind();
        log.info("isPassportBind uid:{},bind:{}", uid, bind);
        return bind;
    }

    public boolean isPassportBindPepc(long uid) {
        GameGateway.GetPassportBindRsp.PassportBindVO result = pubgGameGatewayClient.getBindInfo(uid, GameGateway.Game.GAME_PUBG_MOBILE);
        boolean bind = result != null && result.getBind();
        log.info("isPassportBind uid:{},bind:{}", uid, bind);
        return bind;
    }
}
