package com.yy.gameecology.activity.worker.aspect;

import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.annotation.HdztGroup;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.MDCUtils;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.common.utils.StringUtil;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-08 15:42
 **/
@Aspect
@Component
class SchedulingAspect {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonService commonService;

    private final Tracer TRACER = GlobalOpenTelemetry.getTracer(SchedulingAspect.class.getName());

    /**
     * 注解的方法
     */
    @Pointcut("execution(@org.springframework.scheduling.annotation.Scheduled * *.*(..))")
    public void methodAnnotated() {

    }

    @Around("methodAnnotated() && @annotation(annotation)")
    public Object adviseAnnotatedMethods(ProceedingJoinPoint pjp, Scheduled annotation) throws Throwable {
        if (ShutdownHolder.isShuttingDown()) {
            log.info("服务即将停止,定时器不再触发运行:" + pjp.getTarget().getClass().toString());
            return null;
        }

        MethodSignature signature = (MethodSignature)pjp.getSignature();

        //历史环境没有注解明确标识能执行，则统一拦截不执行
        if (SysEvHelper.checkHistory(StringUtil.EMPTY, false)) {
            ScheduledExt scheduledExt = signature.getMethod().getAnnotation(ScheduledExt.class);
            if (scheduledExt == null || !scheduledExt.historyRun()) {
                return null;
            }
        }

        MDCUtils.putContext("timer="+ signature.getMethod().getName());
        Span span = TRACER.spanBuilder("timer=" +  signature.getMethod().getName()).startSpan();
        try (Scope ignored = span.makeCurrent()) {
            // 没有 Group 注解标记，不用检查分组情况
            HdztGroup hdztGroup = pjp.getTarget().getClass().getAnnotation(HdztGroup.class);
            if (hdztGroup == null) {
                return pjp.proceed();
            }

            // 有 Group 注解标记，但 group 指示为空 或 hdztGroup为空
            String group = SysEvHelper.getGroup();
            if (group.isEmpty() || hdztGroup.value().length == 0) {
                return null;
            }

            // 找到任何一个满足的注解，做正常调用
            int val = Integer.parseInt(group);
            for (int id : hdztGroup.value()) {
                if (id == val) {
                    return pjp.proceed();
                }
            }
        } finally {
            span.end();
        }

        // Group 注解值不匹配，直接返回 null
        return null;
    }
}
