package com.yy.gameecology.activity.bean.mq;

/**
 * desc:陪玩订单
 *
 * @createBy 曾文帜
 * @create 2021-01-24 17:31
 **/
public class PwOrderEvent {
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 用户uid
     */
    private Long userUid;

    /**
     * 陪陪uid
     */
    private Long peipeiUid;

    /**
     * 频道
     */
    private Long sid;

    /**
     * 订单金额(单位: 厘)
     */
    private Long money;

    /**
     * 实际支付金额,扣除优惠券后的金额
     */
    private Long payMoney;

    /**
     * 订单时间
     */
    private Long timestamp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 事件对应的业务来源， zhuiwan或peiwan
     */
    private String source;

    /**
     * 运营频道
     */
    private long opSid;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getUserUid() {
        return userUid;
    }

    public void setUserUid(Long userUid) {
        this.userUid = userUid;
    }

    public Long getPeipeiUid() {
        return peipeiUid;
    }

    public void setPeipeiUid(Long peipeiUid) {
        this.peipeiUid = peipeiUid;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Long getMoney() {
        return money;
    }

    public void setMoney(Long money) {
        this.money = money;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public long getOpSid() {
        return opSid;
    }

    public void setOpSid(long opSid) {
        this.opSid = opSid;
    }

    public Long getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(Long payMoney) {
        this.payMoney = payMoney;
    }
}
