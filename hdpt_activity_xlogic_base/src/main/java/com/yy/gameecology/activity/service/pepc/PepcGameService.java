package com.yy.gameecology.activity.service.pepc;

import cn.hutool.core.convert.Convert;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.client.yrpc.PubgGameGatewayClient;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMemberMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameTeamMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.bean.pepc.*;
import com.yy.gameecology.hdzj.element.component.attr.PepcGameComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.zhuiya.game.gateway.gen.pb.GameGateway;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.eclipse.jetty.util.ajax.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-02 15:51
 **/
@Service
public class PepcGameService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Lazy
    @Autowired
    private PepcGameService myself;

    @Autowired
    private PubgGameGatewayClient pubgGameGatewayClient;

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PepcGameMapper pepcGameMapper;

    @Autowired
    private PepcGameTeamMapper pepcGameTeamMapper;

    @Autowired
    private PepcGameMemberMapper pepcGameMemberMapper;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private PepcTeamMapper pepcTeamMapper;


    public void createRemoteGames(PepcGameComponentAttr attr) {
        List<PepcGame> pubgGames = pepcDao.getPepcGame(attr.getActId(), PepcConst.GameState.INIT);
        if (CollectionUtils.isEmpty(pubgGames)) {
            return;
        }

        for (PepcGame game : pubgGames) {
            myself.doCreateRemoteGame(game);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doCreateRemoteGame(PepcGame game) {

        int rs = pepcGameMapper.updateGameState(game.getActId(), game.getId(), PepcConst.GameState.GAME_CREATING, PepcConst.GameState.INIT, null, null, null);
        if (rs <= 0) {
            log.warn("trying to create remote game already creating");
            return;
        }

        //腾讯无模拟时间，开始报名时间设置为当前物理时间，避免线上验证时，设置了未来模拟时间，无法导入到腾讯，出现大量报错
        //game.getSignUpStartTime()
        Date signUpStartTime = new Date();
        GameGateway.CreatePubgMatchVO result = pubgGameGatewayClient.createPubgGame(game.getId(), game.getGameName(),
                signUpStartTime, game.getSignUpEndTime(), game.getStartTime(), game.getEndTime(),
                0, game.getModuleId(), game.getJoinFunc());

        if (result == null) {
            rs = pepcGameMapper.updateGameState(game.getActId(), game.getId(), PepcConst.GameState.INIT, PepcConst.GameState.GAME_CREATING, null, null, null);
            log.warn("create remote game fail update game state back to init gameId:{} rs:{}", game.getId(), rs);
            return;
        }

        rs = pepcGameMapper.updateGameState(game.getActId(), game.getId(), PepcConst.GameState.GAME_CREATED, PepcConst.GameState.GAME_CREATING, result.getSiteId(), result.getParentId(), result.getMatchId());
        log.info("create remote game success update game with gameId:{}, rs:{}", game.getId(), rs);
    }

    public void createRemoteTeams(PepcGameComponentAttr attr) {

        List<PepcGame> pubgGames = pepcDao.getPepcGame(attr.getActId(), PepcConst.GameState.GAME_CREATED);
        if (CollectionUtils.isEmpty(pubgGames)) {
            return;
        }

        for (PepcGame game : pubgGames) {
            List<PepcGameTeam> gameTeams = pepcDao.getGameTeam(attr.getActId(), game.getId(), PepcConst.GameTeamState.INIT);
            if (CollectionUtils.isEmpty(gameTeams)) {
                continue;
            }

            for (PepcGameTeam team : gameTeams) {
                doCreateRemoteTeam(attr, game, team);
            }
        }
    }

    public void doCreateRemoteTeam(PepcGameComponentAttr attr, PepcGame game, PepcGameTeam team) {

        try {
            GameGateway.CreateTeamRsp.CreateTeamVO result = pubgGameGatewayClient.createTeam(game.getMatchId(), team.getTeamName(), team.getCaptainUid());
            if (result == null) {
                log.warn("create remote team fail update  teamId:{}", team.getId());
                return;
            }

            //无事务，这里要放到最后！！！ 否则中间执行失败，状态已更新就不会再重复执行
            int rs = pepcGameTeamMapper.updateStatus(attr.getActId(), team.getId(), PepcConst.GameTeamState.TEAM_CREATED, PepcConst.GameTeamState.INIT, result.getId(), game.getMatchId());
            log.info("create remote team success update team with teamId:{}, rs:{}", team.getId(), rs);

        } catch (Exception e) {
            log.error("doCreateRemoteTeam fail:", e);
        }
    }


    public void signUpRemoteTeamGames(PepcGameComponentAttr attr) {
        List<PepcGameTeam> gameTeams = pepcDao.getGameTeam(attr.getActId(), PepcConst.GameTeamState.TEAM_CREATED);
        if (CollectionUtils.isEmpty(gameTeams)) {
            return;
        }

        for (PepcGameTeam team : gameTeams) {
            log.info("signUpRemoteTeamGames,teamId:{},matchId:{},txTeamId:{}",team.getId(), team.getMatchId(), team.getTxTeamId());
            boolean success = pubgGameGatewayClient.teamSignUp(team.getId(), team.getMatchId(), team.getTxTeamId());
            if (success) {
                myself.saveSignUpRemoteTeamGames(attr, team);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveSignUpRemoteTeamGames(PepcGameComponentAttr attr, PepcGameTeam team) {
        int rs = pepcGameTeamMapper.updateStatus(attr.getActId(), team.getId(), PepcConst.GameTeamState.TEAM_SIGNED_UP, PepcConst.GameTeamState.TEAM_CREATED, null, null);
        log.info("signUpRemoteTeamGames team success update team with teamId:{}, rs:{}", team.getId(), rs);

        rs = pepcGameMemberMapper
                .updateStatusByGameTeamId(attr.getActId(), team.getGameId(), team.getTeamId(), PepcConst.GameMemberState.TEAM_SIGN_UP, PepcConst.GameMemberState.INIT);
        log.info("updateStatusByTeamId team success update team with teamId:{}, rs:{}", team.getId(), rs);
    }


    public void joinRemoteTeams(PepcGameComponentAttr attr) {

        List<PepcGameMember> signingMembers = pepcDao.getPepcGameMember(attr.getActId(), PepcConst.GameMemberState.TEAM_SIGN_UP);
        if (CollectionUtils.isEmpty(signingMembers)) {
            return;
        }

        Map<Long, PepcGameTeam> gameTeamMap = pepcDao.getGameTeamMap(attr.getActId());


        for (PepcGameMember member : signingMembers) {
            doJointRemoteTeam(attr, gameTeamMap, member);
        }

    }

    public void doJointRemoteTeam(PepcGameComponentAttr attr, Map<Long, PepcGameTeam> gameTeamMap, PepcGameMember member) {
        PepcGameTeam gameTeam = gameTeamMap.get(member.getGameTeamId());
        if (gameTeam == null) {
            log.error("not found gameTeam,member:{}", JSON.toString(member));
            return;
        }
        boolean success;
        if (gameTeam.getCaptainUid().equals(member.getUid())) {
            // 队长不用加入战队
            success = true;
        } else {
            success = pubgGameGatewayClient.joinTeam(gameTeam.getMatchId(), gameTeam.getTxTeamId(), member.getUid());
        }

        if (success) {
            int rs = pepcGameMemberMapper.updateStatus(attr.getActId(), member.getId(), PepcConst.GameMemberState.MEMBER_SIGN_UP, PepcConst.GameMemberState.TEAM_SIGN_UP);
            log.info("join remote team success with memberId:{}, rs:{}", member.getId(), rs);
        }
    }

    public void settleGames(Date now, PepcGameComponentAttr attr) {
        List<PepcGame> gameList = pepcDao.getPepcGame(attr.getActId(), PepcConst.GameState.GAME_CREATED);
        List<PepcGame> startGame = gameList.stream().filter(p -> now.after(p.getStartTime())).toList();
        for (PepcGame game : startGame) {
            log.info("settleGame,actId:{},gameId:{},matchId:{}", game.getActId(), game.getId(), game.getMatchId());
            settleGame(attr, now, game);
        }

    }

    public void settleGame(PepcGameComponentAttr attr, Date now, PepcGame game) {
        GameGateway.QueryMatchBattleDetailRsp.BattleItemVO battleItem = pubgGameGatewayClient.queryMatchBattleDetail(game.getMatchId());
        if (battleItem != null && battleItem.getBattleStatus() == PepcConst.BattleStatus.DESTROYED) {
            myself.saveCancelMatchResult(attr, game, now, PepcConst.GameState.CANCEL_DESTROYED);
        } else if (battleItem != null && battleItem.getBattleStatus() == PepcConst.BattleStatus.SETTLED) {
            List<GameGateway.QueryMatchBattleDetailRsp.BattleItemVO.UserItemVO> userList = battleItem.getUserListList();
            if (CollectionUtils.isEmpty(userList)) {
                log.error("battle state is settled but user list is empty");
                return;
            }
            myself.saveMatchResult(attr, game, userList, now);
        } else {
            Date ultimateSettleTime = DateUtils.addMinutes(game.getStartTime(), attr.getSettleTimeMin());
            // 超过了最后的结算时间还是没有结果，则触发最终的结算
            if (now.after(ultimateSettleTime)) {
                myself.saveCancelMatchResult(attr, game, now, PepcConst.GameState.NO_RESULT);
            }
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveMatchResult(PepcGameComponentAttr attr, PepcGame game, List<GameGateway.QueryMatchBattleDetailRsp.BattleItemVO.UserItemVO> userList, Date now) {
        boolean validResult = userList.stream().anyMatch(userItem -> userItem.getCurStatus() == PepcConst.UserStatus.CLOSED);
        if (!validResult) {
            log.error("saveMatchResult but no user status is closed gameId:{}", game.getId());
            return;
        }

        int rs = pepcGameMapper.updateGameState(attr.getActId(), game.getId(), PepcConst.GameState.CLOSE, PepcConst.GameState.GAME_CREATED, null, null, null);
        log.info("saveMatchResult update game state with gameId:{} rs:{}", game.getId(), rs);

        if (rs <= 0) {
            return;
        }

        List<Long> inGameUid = new ArrayList<>(userList.size());
        for (GameGateway.QueryMatchBattleDetailRsp.BattleItemVO.UserItemVO user : userList) {
            long uid = Convert.toLong(user.getUid(), 0L);
            if (uid <= 0) {
                log.error("saveMatchResult rank id not uid:{}", user.getUid());
                continue;
            }

            PepcGameMember member = pepcDao.getPepcGameMember(attr.getActId(), game.getId(), uid);
            if (member == null) {
                log.error("saveMatchResult can not get game member of gameId:{} uid:{}", game.getId(), uid);
                continue;
            }

            int userStatus = user.getCurStatus();
            if (userStatus != PepcConst.UserStatus.CLOSED) {
                log.warn("saveMatchResult but userStatus is not closed uid:{} userStatus:{}", uid, userStatus);
                continue;
            }

            rs = pepcDao.savePepcGameMemberMatchResult(member.getId(), PepcConst.GameMemberState.RESULTED, user.getTeamRank(), String.valueOf(user.getDamageAmount()), String.valueOf(user.getKillingCount()), String.valueOf(user.getAssistCount()), "-1", "-1", "-1");
            log.info("saveMatchResult update match result with gameId:{} uid:{} rs:{}", game.getId(), uid, rs);
            inGameUid.add(uid);
        }

        PepcGameEndEvent event = new PepcGameEndEvent();
        event.setSeq("pepc_game_end:" + attr.getActId() + "_" + game.getId());
        event.setActId(attr.getActId());
        event.setGameId(game.getId());
        event.setJoinFunc(game.getJoinFunc());
        event.setState(PepcConst.GameState.CLOSE);
        event.setStartTime(game.getStartTime().getTime());
        event.setInGameUid(inGameUid);

        kafkaService.sendHdzkCommonEvent(event);
        log.info("saveMatchResult sent kafka event with gameId:{} inGameUid size:{}", game.getId(), inGameUid.size());

    }

    @Transactional(rollbackFor = Throwable.class)
    public void saveCancelMatchResult(PepcGameComponentAttr attr, PepcGame game, Date now, Integer state) {
        int rs = pepcGameMapper.updateGameState(attr.getActId(), game.getId(), state, PepcConst.GameState.GAME_CREATED, null, null, null);
        log.info("saveMatchResult update game state with gameId:{} rs:{}", game.getId(), rs);

        if (rs <= 0) {
            return;
        }

        PepcGameEndEvent event = new PepcGameEndEvent();
        event.setSeq("pepc_game_end:" + attr.getActId() + "_" + game.getId());
        event.setActId(attr.getActId());
        event.setGameId(game.getId());
        event.setJoinFunc(game.getJoinFunc());
        event.setState(state);
        event.setStartTime(game.getStartTime().getTime());
        event.setInGameUid(Lists.newArrayList());

        kafkaService.sendHdzkCommonEvent(event);
        log.info("saveCancelMatchResult sent kafka event with gameId:{}", game.getId());

    }

    public PepcGameResultVo queryGameResult(PepcPhaseComponentAttr attr, Long cmptInx, Long gameId) {
        PepcGameResultVo vo = new PepcGameResultVo();
        List<PepcGameResultItemVo> teams = Lists.newArrayList();

        long actId = attr.getActId();
        List<PepcGameTeam> pepcGameTeams = pepcDao.getGameTeamByGameId(actId, gameId);
        if (CollectionUtils.isEmpty(pepcGameTeams)) {
            return vo;
        }
        List<Long> gameIds = pepcGameTeams.stream().map(PepcGameTeam::getGameId).distinct().toList();
        List<PepcGameMember> pepcGameMembers = pepcGameMemberMapper.select(actId, gameIds);
        List<Long> uids = pepcGameMembers.stream().map(PepcGameMember::getUid).distinct().toList();
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(uids, Template.unknown);


        for (PepcGameTeam gameTeam : pepcGameTeams) {
            getTeamGameResult(attr, gameTeam, userInfoVoMap, pepcGameMembers, teams);
        }

        //按照分数从大到小排序
        teams = teams.stream().sorted(Comparator.comparing(PepcGameResultItemVo::getTeamScore).reversed()).toList();
        vo.setTeams(teams);
        return vo;
    }

    private static void getTeamGameResult(PepcPhaseComponentAttr attr, PepcGameTeam gameTeam, Map<Long, UserInfoVo> userInfoVoMap, List<PepcGameMember> pepcGameMembers, List<PepcGameResultItemVo> teams) {
        PepcGameResultItemVo itemVo = new PepcGameResultItemVo();
        itemVo.setTeamId(gameTeam.getTeamId());
        itemVo.setTeamName(gameTeam.getTeamName());
        var captain = userInfoVoMap.get(gameTeam.getCaptainUid());
        if (captain != null) {
            itemVo.setHeader(captain.getAvatarUrl());
        }
        itemVo.setTeamScore(gameTeam.getScore() / attr.getScoreExtraDigits());

        long totalElimination = 0;
        long totalAssist = 0;
        long totalDamage = 0;

        List<PepcGameMember> teamMembers = pepcGameMembers.stream()
                .filter(m -> gameTeam.getTeamId().equals(m.getTeamId())).toList();
        List<PepcGameMemberResultVo> memberResultVos = Lists.newArrayList();
        for (PepcGameMember member : teamMembers) {
            PepcGameMemberResultVo memberResultVo = new PepcGameMemberResultVo();
            memberResultVo.setUid(member.getUid());

            long elimination = Convert.toLong(member.getElimination(), 0L);
            memberResultVo.setElimination(elimination);
            totalElimination += elimination;

            long assist = Convert.toLong(member.getAssist(), 0L);
            memberResultVo.setAssist(assist);
            totalAssist += assist;

            long damage = Convert.toLong(member.getHarm(), 0L);
            damage = damage / 100;
            memberResultVo.setDamage(damage);
            totalDamage += damage;

            var user = userInfoVoMap.get(member.getUid());
            if (user != null) {
                memberResultVo.setNick(user.getNick());
                memberResultVo.setHeader(user.getAvatarUrl());
            }

            memberResultVos.add(memberResultVo);
        }
        itemVo.setMembers(memberResultVos);
        itemVo.setElimination(totalElimination);
        itemVo.setAssist(totalAssist);
        itemVo.setDamage(totalDamage);

        teams.add(itemVo);
    }


    public Response<String> jumpGame(PepcGameComponentAttr attr, Date now, Long uid, Long gameId, Long sid, Long ssid) {
        var game = pepcDao.getPepcGameById(attr.getActId(), gameId);
        if (game == null) {
            return Response.fail(461, "比赛不存在");
        }

        Date jumpGameTime = DateUtils.addMinutes(game.getStartTime(), -attr.getJumpGameMin());
        if (now.before(jumpGameTime)) {
            return Response.fail(462, "比赛尚未开始，请等待比赛开始后再进游戏吧");
        }

        if (game.getState() != PepcConst.GameState.GAME_CREATED) {
            return Response.fail(463, "比赛已结束或已被取消，敬请等待下一场比赛开始");
        }

        PepcGameMember gameMember = pepcDao.getPepcGameMember(attr.getActId(), gameId, uid);
        if (gameMember == null) {
            return Response.fail(465, "您非当场比赛参赛用户，无法进入比赛");
        }

        //腾讯有时间控制
//        Date jumpDeadTime = DateUtils.addMinutes(game.getStartTime(), attr.getJumpRoomDeadMin());
//        if (now.after(jumpDeadTime)) {
//            return Response.fail(464, "由于您到场较晚，无法加入比赛。下次对局记得早点到场哦");
//        }


        GameGateway.EnterMatchRsp.EnterMatchVO enterVo = pubgGameGatewayClient.enterMatch(game.getSiteId(), game.getParentId(), game.getMatchId(), uid);
        if (enterVo == null) {
            return Response.fail(500, "网络开小差了，请售后再试试看");
        }

        String url = enterVo.getMatchUrl() + "?" + enterVo.getParameters();
        if (StringUtils.isNotEmpty(enterVo.getParameters()) && !enterVo.getParameters().endsWith(StringUtil.AND)) {
            url += StringUtil.AND;
        }
        url += "ignore_register=1";
        log.info("getJumpUrl done with gameId:{} uid:{} url:{}", game.getId(), uid, url);


        int updateRes = pepcGameMemberMapper.updateJoinGameTime(attr.getActId(), gameId, uid, now);
        log.info("updateJoinGameTime actId:{},gameId:{},uid:{},updateRes:{}", attr.getActId(), gameId, uid, updateRes);

        return Response.success(url);
    }


    /**
     * 查询当前用户的赛事状态，用户要报名成功了才来请求这个接口，否则可能会返回“赛事未开始”
     *
     * @param attr
     * @param uid
     * @return @see com.yy.gameecology.common.consts.PepcConst.TeamCurrentGameState
     */
    public PepcTeamCurrentGameVo queryTeamCurrentGameStatus(Date now, PepcPhaseComponentAttr attr, long uid) {
        long actId = attr.getActId();
        PepcTeamCurrentGameVo vo = new PepcTeamCurrentGameVo();
        List<PepcGameMember> gameMembers = pepcDao.getPepcGameMember(actId, uid);
        //赛事未开始
        if (CollectionUtils.isEmpty(gameMembers)) {
            vo.setState(PepcConst.TeamCurrentGameState.NOT_BEGIN);
            return vo;
        }

        List<Long> gameId = gameMembers.stream().map(PepcGameMember::getGameId).toList();
        //按时间顺序排
        List<PepcGame> pepcGames = pepcGameMapper.selectPepcGameByGameIds(actId, gameId);
        pepcGames = pepcGames.stream().sorted(Comparator.comparing(PepcGame::getStartTime)).toList();

        //---第一场赛事未开始
        PepcGame firstGame = pepcGames.getFirst();
        Date beginEnterGame = DateUtils.addMinutes(firstGame.getStartTime(), attr.getShowMyGameStatePlayingBegin());
        if (now.before(beginEnterGame)) {
            vo.setState(PepcConst.TeamCurrentGameState.NOT_BEGIN);
            vo.setFirstGameStartTime(firstGame.getStartTime().getTime());
            return vo;
        }

        //---第一场开始开始后，存在未结算的赛事
        List<PepcGame> notCloseGame = pepcGames
                .stream()
                .filter(p -> !PepcConst.GameState.FINAL_STATE.contains(p.getState())).toList();
        if (CollectionUtils.isNotEmpty(notCloseGame)) {
            PepcGame notCloseFirstGame = notCloseGame.getFirst();
            Date enterGameTime = DateUtils.addMinutes(notCloseFirstGame.getStartTime(), attr.getShowMyGameStatePlayingBegin());
            if (now.after(enterGameTime)) {
                //赛事开始，可进入比赛
                vo.setGameStartTime(notCloseFirstGame.getStartTime().getTime());
                vo.setState(PepcConst.TeamCurrentGameState.ENTER_GAME);
                return vo;
            } else {
                //有下一场比赛，按钮展示为【即将开始】
                vo.setState(PepcConst.TeamCurrentGameState.WAIT_NEXT_GAME);
                vo.setNextGameStartTime(notCloseFirstGame.getStartTime().getTime());
                return vo;
            }
        }

        boolean allGameIsOver = pepcGames.stream().allMatch(p -> PepcConst.GameState.FINAL_STATE.contains(p.getState()));
        if (allGameIsOver) {
            var lastGame = pepcGames.getLast();
            PepcPhaseInfo nextPhase = pepcDao.getPepcPhaseInfo(actId, lastGame.getPhaseId() + 1);
            //当介于中间态时（其他队伍没有完成比赛，未完成下一阶段对局分配），按钮展示为【等待赛果】
            if (nextPhase != null && PepcConst.PhaseInfoState.BEFORE_INIT_DATA.contains(nextPhase.getState())) {
                vo.setState(PepcConst.TeamCurrentGameState.SETTLE);
            } else {
                //本期赛事流程走完时，按钮展示为【比赛已结束】| 被淘汰+全部已结束
                vo.setState(PepcConst.TeamCurrentGameState.GAME_OVER);
            }
        }


        //error 以上逻辑未涵盖到所有情况，走到这里就有bug
        return vo;
    }

    /**
     * 返回null时代表，当前时间段不存在待进入的比赛
     */
    public PepcChannelGameVo queryChannelCurrentGame(long actId, long sid, long ssid) {
        List<PepcGameTeam> gameTeams = pepcDao.getGameTeamBySid(actId, sid, ssid);
        if (CollectionUtils.isEmpty(gameTeams)) {
            return null;
        }
        List<Long> gameIds = gameTeams.stream().map(PepcGameTeam::getGameId).toList();
        List<PepcGame> games = pepcGameMapper.selectPepcGameByGameIds(actId, gameIds);
        List<PepcGame> notCloseGame = games.stream()
                .filter(p -> !PepcConst.GameState.FINAL_STATE.contains(p.getState()))
                .sorted(Comparator.comparing(PepcGame::getStartTime))
                .toList();
        if (CollectionUtils.isEmpty(notCloseGame)) {
            return null;
        }
        PepcGame nearGame = notCloseGame.getFirst();

        PepcChannelGameVo vo = new PepcChannelGameVo();
        vo.setGameId(nearGame.getId());
        vo.setRound(nearGame.getRound());
        vo.setStartTime(nearGame.getStartTime());

        var group = pepcDao.getPepcTeamGroup(nearGame.getActId(), nearGame.getPhaseId(), nearGame.getGroupCode());
        if (CollectionUtils.isNotEmpty(group)) {
            vo.setGroupName(group.getFirst().getGroupName());
        }

        return vo;
    }

    public boolean showGameLayer(PepcGameComponentAttr attr, long sid, long ssid) {
        Date now = commonService.getNow(attr.getActId());
        boolean inActTime = actInfoService.inActTime(attr.getActId());
        if (!inActTime) {
            return false;
        }
        PepcTeam team = pepcTeamMapper.selectTeamBySid(attr.getActId(), sid, ssid);
        if (team == null) {
            return false;
        }
        if (team.getState() == PepcConst.PhaseTeamState.INIT) {
           return true;
        }
        boolean show = false;
        if (team.getState() == PepcConst.PhaseTeamState.SUCC) {
            // 分组成功 开始赛程
            PepcChannelGameVo channelVo = queryChannelCurrentGame(attr.getActId(), sid, ssid);
//            log.info("queryChannelLayerInfo team state {} {} {} {}",team, channelVo , DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"), DateUtil.format(channelVo.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            if (channelVo != null && channelVo.getGameId() > 0 && now.after(DateUtils.addMinutes(channelVo.getStartTime(),(-1)*attr.getJumpGameMin())) ) {
                log.info("queryChannelLayerInfo team state {} channelVo {}",team,channelVo);
                show = true;
            }
        }
        return show;
    }

    public List<PepcGame> getGameByState(long actId, int state) {
        return pepcDao.getPepcGame(actId, state);
    }
}
