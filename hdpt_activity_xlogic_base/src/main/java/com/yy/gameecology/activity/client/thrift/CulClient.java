package com.yy.gameecology.activity.client.thrift;

import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.common.annotation.Report;
import com.yy.java.cul.CulService;
import com.yy.java.cul.thrift.UserChannelInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * desc:新sa
 *
 * @createBy 曾文帜
 * @create 2019-12-12 10:45
 **/
@Component
public class CulClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CulService culService;

    @Report
    public List<Long> queryOnMicList(long sid, long ssid) {

        try {
            Map<Long, List<Long>> maixuList = culService.queryChannelMaixuList(sid, Collections.singletonList(ssid));
            return maixuList.get(ssid);
        } catch (Exception e) {
            log.error("queryOnMicList error,sid:{},ssid:{} ", sid, ssid, e);
            return new ArrayList<>();
        }
    }

    @Report
    public Map<Long, List<Long>> batchQueryOnMicList(long sid, Set<Long> subSids) {
        try {
            return culService.queryChannelMaixuList(sid, List.copyOf(subSids));
        } catch (Exception e) {
            log.error("batchQueryOnMicList error,sid:{},ssid:{}", sid, subSids, e);
            return null;
        }
    }

    @Report
    public ChannelInfoVo queryUserChannel(long uid) {
        try {
            Map<Long, List<UserChannelInfo>> userChannelInfos = culService.queryUserChannelInfo(Collections.singletonList(uid));
            if (MapUtils.isEmpty(userChannelInfos) || !userChannelInfos.containsKey(uid) || CollectionUtils.isEmpty(userChannelInfos.get(uid))) {
                return null;
            }

            //这里有潜在问题，有的特殊业务可能会在多个频道，例如web 具体咨询 龚子建
            UserChannelInfo userChannelInfo = userChannelInfos.get(uid).get(0);
            ChannelInfoVo vo = new ChannelInfoVo();
            vo.setSid(userChannelInfo.getTopsid());
            vo.setSsid(userChannelInfo.getSubsid());
            vo.setTimestamp(userChannelInfo.getTimestamp());
            return vo;
        } catch (Exception e) {
            log.error("queryUserChannel error,uid:{}", uid, e);
            return null;
        }
    }

    @Report
    public List<Long> queryUserChannel(List<Long> uids, long sid, long ssid) {
        List<Long> currentChannelUids = new ArrayList<>();

        if(uids == null || uids.isEmpty()) {
            return null;
        }
        // Split uids into chunks of 1000
        int chunkSize = 1000;
        for (int i = 0; i < uids.size(); i += chunkSize) {
            int end = Math.min(i + chunkSize, uids.size());
            List<Long> chunk = uids.subList(i, end);

            try {
                Map<Long, List<UserChannelInfo>> userChannelInfos = culService.queryUserChannelInfo(chunk);
                if (MapUtils.isEmpty(userChannelInfos)) {
                    continue; // Skip if no channel info
                }

                for (Long uid : userChannelInfos.keySet()) {
                    List<UserChannelInfo> channelInfos = userChannelInfos.get(uid);
                        for (UserChannelInfo info : channelInfos) {
                            if (info.getTopsid() == sid && info.getSubsid() == ssid) {
                                currentChannelUids.add(uid);
                            }
                        }
                }
            } catch (Exception e) {
                log.error("queryUserChannel error,uids:{}, sid:{}, ssid:{}, e:{}", uids, sid, ssid, e.getMessage(), e);
                return null;
            }
        }
        return currentChannelUids.isEmpty() ? null : currentChannelUids;
    }
}
