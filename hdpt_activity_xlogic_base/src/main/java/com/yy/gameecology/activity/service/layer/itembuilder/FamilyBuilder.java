package com.yy.gameecology.activity.service.layer.itembuilder;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.db.model.gameecology.ActLayerViewDefine;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/10/18 17:16
 **/
@Component
public class FamilyBuilder extends AbstractLayerItemBuilder {
    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Override
    public Set<String> getItemKeys() {
        return Collections.singleton(LayerItemTypeKey.FAMILY);
    }

    @Override
    public List<String> getMemberIds(ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, OnlineChannelInfo onlineChannel) {
        //联运的房间不一定是技能卡房
//        if ((long) BusiId.SKILL_CARD.getValue() != busiId) {
//            return new ArrayList<>();
//        }
        RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(onlineChannel.getSsid());
        if (roomInfo == null) {
            return new ArrayList<>();
        }

        return Arrays.asList(String.valueOf(roomInfo.getFamilyId()));
    }

    @Override
    public List<LayerMemberItem> build(Date now, ActivityInfoVo actInfo, Long busiId, ActLayerViewDefine viewDefine, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return null;
        }
        List<LayerMemberItem> familyItems = Lists.newArrayList();

        LayerMemberItem layerMemberItem = getFamilyItemInfo(actInfo, viewDefine, busiId, memberIds.get(0), now);
        // 扩展信息
        Map<String, Object> itemExtInfo = actLayerInfoExtService.extendLayerMemberItem(actInfo.getActId(), layerMemberItem);
        if (MapUtils.isNotEmpty(itemExtInfo)) {
            layerMemberItem.getExt().putAll(itemExtInfo);
        }
        familyItems.add(layerMemberItem);

        return familyItems;
    }

    public LayerMemberItem getFamilyItemInfo(ActivityInfoVo actInfo,
                                             ActLayerViewDefine viewDefine,
                                             Long busiId,
                                             String memberId,
                                             Date now) {

        Clock clock = new Clock();
        if (StringUtil.isEmpty(memberId)) {
            return null;
        }

        Long actId = actInfo.getActId();

        LayerMemberItem itemRes = buildMemberItem(actInfo, viewDefine, busiId, memberId, now);

        return itemRes;
    }

    @Override
    public void fillLayerBroadcastInfo(ActLayerViewDefine viewDefine, LayerBroadcastInfo target, List<LayerMemberItem> source, List<String> memberIds, Map<String, Object> ext) {
        if (CollectionUtils.isEmpty(source)) {
            return;
        }
        target.getExtMemberItem().addAll(source);
    }

    @Override
    protected void setMemberInfo(String memberId, LayerMemberItem itemRes) {
        long familyId = Convert.toLong(memberId, 0);
        FamilyBasicInfo familyBasicInfo = zhuiWanPrizeIssueServiceClient.getFamilyBasicInfo(familyId);
        if (familyBasicInfo != null) {
            itemRes.setNickName(Base64Utils.encodeToString(Convert.toString(familyBasicInfo.getFamilyName()).getBytes()));
            itemRes.setLogo(familyBasicInfo.getCover() == null ? "" : familyBasicInfo.getCover());
        }
    }
}
