package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.client.yrpc.HdptTemplateClient;
import com.yy.gameecology.activity.worker.timer.AutoCreateTableTimer;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5129PushUserInfoMapper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.WowGoldActivityCcontrolComponent;
import com.yy.gameecology.hdzj.element.component.WowGoldActivityLoginComponent;
import com.yy.gameecology.hdzj.element.component.WowGoldActivityPushComponent;
import com.yy.gameecology.hdzj.element.component.WowTaskComponent;
import com.yy.gameecology.hdzj.element.component.attr.*;

import lombok.Data;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;

import java.lang.reflect.Method;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties", "classpath:env/local/application-inner.properties"})
public class WowActivityComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "3");
    }

    private static final long ActId = 2025033002;

    @Autowired
    private WowGoldActivityCcontrolComponent wowGoldActivityCcontrolComponent;

    @Autowired
    private WowGoldActivityLoginComponent wowGoldActivityLoginComponent;

    @Autowired
    private WowGoldActivityPushComponent wowGoldActivityPushComponent;

    @Autowired
    private WowTaskComponent wowTaskComponent;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private Cmpt5129PushUserInfoMapper cmpt5129PushUserInfoMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AutoCreateTableTimer autoCreateTableTimer;

    @Autowired
    private HdptTemplateClient hdptTemplateClient;

    @Test
    public void onSyncHiidoUsersTest() throws InterruptedException {
        long componentUseIndex = 810;
        WowGoldActivityCcontrolComponentAttr attr = wowGoldActivityCcontrolComponent.getComponentAttr(ActId, componentUseIndex);

        wowGoldActivityCcontrolComponent.sync((int)ActId,810,"2025-03-26");
//        wowGoldActivityCcontrolComponent.sync((int)ActId,810,"2025-03-24");

        Thread.sleep(40000);
    }

    @Test
    public void queryHiidoUsersTest() throws InterruptedException {
        long componentUseIndex = 810;
        WowGoldActivityCcontrolComponentAttr attr = wowGoldActivityCcontrolComponent.getComponentAttr(ActId, componentUseIndex);

//        testQuery("2025-03-03",1);
//
//        testQuery("2025-03-03",10);
//
//        testQuery("2025-03-03",50);
//
//        testQuery("2025-03-03",60);
//
//        testQuery("2025-03-03",70);
//
//        testQuery("2025-03-03",80);
//
//        testQuery("2025-03-03",90);
//
//        testQuery("2025-03-03",100);

    }

    public void testQuery(String ymd,int pagetIndex){
        long startTime = System.currentTimeMillis();
        wowGoldActivityCcontrolComponent.queryGameUsers(ymd,pagetIndex,500);
        long endTime = System.currentTimeMillis();
        log.info("testQuery ymd{},index{},ms:{}", ymd,pagetIndex,endTime-startTime);
    }

    @Test
    public void onLoginTest() {
        long componentUseIndex = 810;
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, componentUseIndex);

//        var rep =  wowGoldActivityLoginComponent.getUserState((int)ActId,810);
//        log.info("resp {}",JSON.toJSONString(rep));

//        rep =  wowGoldActivityLoginComponent.getUserState((int)ActId,810);
//        log.info("resp {}",JSON.toJSONString(rep));
       long uid = 1000002563;
       String member = Convert.toString(uid);
       var ret =  wowGoldActivityLoginComponent.getWhiteListState(ActId,810,member);
       log.info("onLoginTest ret:{}", ret);

        uid = 1000025309;
        member = Convert.toString(uid);
        ret =  wowGoldActivityLoginComponent.getWhiteListState(ActId,810,member);
        log.info("onLoginTest ret:{}", ret);

        uid = 1000025319;
        member = Convert.toString(uid);
        ret =  wowGoldActivityLoginComponent.getWhiteListState(ActId,810,member);
        log.info("onLoginTest ret:{}", ret);

        uid = 3039029694L;
        Long  uid1 = uid;
        member = Convert.toString(uid1);
        ret =  wowGoldActivityLoginComponent.getWhiteListState(ActId,810,member);
        log.info("onLoginTest ret:{}", ret);

        uid = 3038699319L;
        uid1 = uid;
        member = Convert.toString(uid1);
        ret =  wowGoldActivityLoginComponent.getWhiteListState(ActId,810,member);
        log.info("onLoginTest ret:{}", ret);
    }

    @Test
    public void onLotteryTest() {
        long componentUseIndex = 810;
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, componentUseIndex);


        var rep =  wowGoldActivityLoginComponent.getUserState((int)ActId,810);
        log.info("resp {}",JSON.toJSONString(rep));
        rep =  wowGoldActivityLoginComponent.lottery((int)ActId,810,"yypc");
        log.info("resp {}",JSON.toJSONString(rep));
        rep =  wowGoldActivityLoginComponent.getUserState((int)ActId,810);
        log.info("resp {}",JSON.toJSONString(rep));
    }

    @Test
    public void onPopWindowTest() {
        long componentUseIndex = 810;
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, componentUseIndex);


        var rep =  wowGoldActivityLoginComponent.getAppPopupUrl((int)ActId,810,50013181,"b7952b69da85bb65f1506a84def866b5");
        log.info("resp {}",JSON.toJSONString(rep));
    }

    @Test
    public void getLotteryStateTest() {
        long componentUseIndex = 810;
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, componentUseIndex);


//        var rep =  wowGoldActivityLoginComponent.getLotteryState((int)ActId,810,"yypc");
//        log.info("resp {}",JSON.toJSONString(rep));
    }

    @Test
    public void getPushStateTest() {
        long componentUseIndex = 810;
        WowGoldActivityPushComponentAttr attr = wowGoldActivityPushComponent.getComponentAttr(ActId, 810);

        var rep =  wowGoldActivityPushComponent.getPushState((int)ActId,810);
        log.info("resp {}",JSON.toJSONString(rep));
//        var rep =  wowGoldActivityLoginComponent.getLotteryState((int)ActId,810,"yypc");
//        log.info("resp {}",JSON.toJSONString(rep));
    }

    @Test
    public void updatePushStateTest() {
        long componentUseIndex = 810;
        WowGoldActivityPushComponentAttr attr = wowGoldActivityPushComponent.getComponentAttr(ActId, 810);

        var rep =  wowGoldActivityPushComponent.setPushState((int)ActId,810,1);
        log.info("resp {}",JSON.toJSONString(rep));
//        var rep =  wowGoldActivityLoginComponent.getLotteryState((int)ActId,810,"yypc");
//        log.info("resp {}",JSON.toJSONString(rep));
    }

    @Test
    public void batchGetPushStateTest() {
        long componentUseIndex = 810;
        WowGoldActivityPushComponentAttr attr = wowGoldActivityPushComponent.getComponentAttr(ActId, 810);

         wowGoldActivityPushComponent.sendPush();
    }

    @Test
    public void setPushLastTimeTest() {
        long componentUseIndex = 810;
        WowGoldActivityPushComponentAttr attr = wowGoldActivityPushComponent.getComponentAttr(ActId, 810);

        DayTaskCompleteEvent event = new DayTaskCompleteEvent();
        event.setMemberId("50013181");
        wowGoldActivityPushComponent.onDayTaskCompleteEvent(event,attr);

//        wowGoldActivityPushComponent.setPushState((int)ActId,810,1;
        return ;
    }

    @Test
    public void testZhuiwanLogin() {
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, 810);
        ZhuiwanLoginEvent event = new ZhuiwanLoginEvent();
        event.setUid(50013181);
        event.setHdid("5d1a6030084bee36f4641dc4fc82d433");
        // 50013181 b7952b69da85bb65f1506a84def866b5
        //event.setFirstLogin(true);
//        event.setClientType(1);
        event.setApp("yomi");

        wowGoldActivityLoginComponent.onZhuiwanLoginEvent(event, attr);
    }

    @Test
    public void testDeleteLottery() {
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, 810);

        wowGoldActivityLoginComponent.removeLotteryTool((int) ActId, 810,50013181);
    }

    @Test
    public void getReward() {
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, 810);

        wowGoldActivityLoginComponent.getLoginReward((int) ActId, 810,1,null,null,"","","");
    }


    @Test
    public void onDateReportTest() {
        long componentUseIndex = 810;
        WowGoldActivityLoginComponentAttr attr = wowGoldActivityLoginComponent.getComponentAttr(ActId, 810);
        WowGoldActivityCcontrolComponentAttr cattr = wowGoldActivityCcontrolComponent.getComponentAttr(ActId, 810);

       // wowGoldActivityLoginComponent.reportSyncDate();
//        String seq = "1233gfgdddg";
//        Date info = DateUtil.getDate("2025-04-12 00:00:00");
//        wowGoldActivityLoginComponent.addLoginLimitAmount(attr,seq,info,1000);
//        wowGoldActivityLoginComponent.reportSyncDate();

        Date now = DateUtil.getDate("2025-04-09 00:00:00");
        String dateStr = DateUtil.format(now, DateUtil.PATTERN_TYPE5);

        wowGoldActivityCcontrolComponent.updateWhitelistStatic(cattr,dateStr,500,3);
        wowGoldActivityCcontrolComponent.updateWhitelistStatic(cattr,dateStr,500,2);
        var ret = wowGoldActivityCcontrolComponent.getWhiteStatics(ActId,810,dateStr);
        log.info("resp {}",JSON.toJSONString(ret));
    }

    @Test
    public void topicTest(){
        zhuiWanPrizeIssueServiceClient.queryUserPostCount(3040581588L, DateUtil.getDate("2025-01-24 14:58:17"), new Date(), 103);
    }

    @Test
    public void selectTest(){
        cmpt5129PushUserInfoMapper.selectPushMembers(2025033002L,DateUtil.getDate("2025-04-20 00:00:01"),
                DateUtil.getDate("2025-04-20 23:59:59")
        ,1000,1, 0, 100);
    }

    @Test
    public void testGet() {
        String url = "https://turnover-pre.yy.com/turnover?appId=turnover&sign=8ae9cbab98b76d4647a1a8a7c97b2901&data={\"appId\":2,\"jsonMsg\":{\"uid\":0,\"expand\":\"{\\\"filterPropsTypes\\\": \\\"19\\\"}\",\"appId\":2,\"cmd\":1010},\"cmd\":1010,\"version\":0}";
        DefaultUriBuilderFactory factory = new DefaultUriBuilderFactory();
        factory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.NONE);  // 关闭自动编码
        restTemplate.setUriTemplateHandler(factory);
        restTemplate.getForObject("https://turnover-pre.yy.com/turnover?appId=turnover&sign=8ae9cbab98b76d4647a1a8a7c97b2901&data=%7B%22appId%22%3A2%2C%22jsonMsg%22%3A%7B%22uid%22%3A0%2C%22expand%22%3A%22%7B%5C%22filterPropsTypes%5C%22%3A+%5C%2219%5C%22%7D%22%2C%22appId%22%3A2%2C%22cmd%22%3A1010%7D%2C%22cmd%22%3A1010%2C%22version%22%3A0%7D", String.class);
    }

    @Test
    public void testCreateTable(){
        hdptTemplateClient.getReadProxy().getEffectXlogicTableConfig(1);
        autoCreateTableTimer.runAutoCreateTable();
    }


}
