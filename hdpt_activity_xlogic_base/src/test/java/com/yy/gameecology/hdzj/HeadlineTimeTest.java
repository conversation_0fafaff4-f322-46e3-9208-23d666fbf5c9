package com.yy.gameecology.hdzj;

import com.google.common.collect.Lists;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.element.component.ChannelChatLotteryComponent;
import com.yy.gameecology.hdzj.element.component.HeadlineTimeComponent;
import com.yy.gameecology.hdzj.element.component.LatestCpComponent;
import com.yy.gameecology.hdzj.element.component.PuzzleComponent;
import com.yy.gameecology.hdzj.element.component.attr.ChannelChatLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.HeadlineTimeComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.LatestCpComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties", "classpath:env/local/application-inner.properties"})
public class HeadlineTimeTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    final long ACT_ID = 2025051001;

    @Autowired
    private HeadlineTimeComponent headlineTimeComponent;

    @Autowired
    private LatestCpComponent latestCpComponent;

    @Autowired
    private PuzzleComponent puzzleComponent;

    @Autowired
    private ChannelChatLotteryComponent channelChatLotteryComponent;

    @Autowired
    protected ActRedisGroupDao actRedisDao;

    @Autowired
    protected RedisConfigManager redisConfigManager;


    @Autowired
    private ChannelChatLotteryComponent chatLotteryBox;


    @Test
    public void onRankingTimeEndTest() {
        RankingTimeEnd event = new RankingTimeEnd();
        event.setEndTime("2025-05-13 01:59:59");
        event.setRankId(18);
        event.setActId(ACT_ID);
        event.setSeq("20250221899");
        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        headlineTimeComponent.onRankingTimeEnd(event,attr);
    }

    @Test
    public void getPastCpTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-03-17 17:59:59");
//        event.setRankId(10);
//        event.setActId(ACT_ID);
//        event.setSeq("202502211");
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        String dateStr = "2025051221";
        Response<HeadlineTimeComponent.BestCpData> resp =  headlineTimeComponent.getPastBestCp(ACT_ID,500,dateStr);
        log.info("resp {}",resp);
    }

    @Test
    public void onLastEnterTest() {
        RankingScoreChanged event = new RankingScoreChanged();
        event.setTimestamp("2025-03-17 20:56:59");
        event.setRankId(10);
        event.setActId(ACT_ID);
        event.setSeq("20250313111");
        Map<Long, String>  actors = new HashMap<>();
        actors.put(81050L, "1454054224_81454054224");
        event.setActors(actors);
        event.setMember("50013181|1080293145");
        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        //headlineTimeComponent.onRankScoreChange(event,attr);
    }

    @Test
    public void getBestCpTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-03-17 17:59:59");
//        event.setRankId(10);
//        event.setActId(ACT_ID);
//        event.setSeq("202502211");
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
//        String dateStr = "2025031718";
        Response<ChannelChatLotteryComponent.ChatLotteryBoxListRsp> resp =  chatLotteryBox.getBestCp(ACT_ID,810);
        log.info("{}",resp);
    }

@Test
 public void getChatBoxTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-03-17 17:59:59");
//        event.setRankId(10);
//        event.setActId(ACT_ID);
//        event.setSeq("202502211");
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
//        String dateStr = "2025031718";
//        Response<ChannelChatLotteryComponent.ChatLotteryBoxListRsp> resp =  chatLotteryBox.getBestCp(ACT_ID,810);
//        log.info("{}",resp);
    ChannelChatLotteryComponentAttr attr = channelChatLotteryComponent.getUniqueComponentAttr(ACT_ID);

    String currentTing = 1454054224L + "_" + 1454054224L;
    String lotteryBoxKey = "act:2025034001:hdzj_cmpt:5114:810:chat_lottery_box";
    long nowTime = System.currentTimeMillis();
    Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
            lotteryBoxKey, nowTime, Long.MAX_VALUE);

   log.info(" nowTime:{} {}",nowTime,rawSet);


   }

    @Test
    public void enterChannelTest() {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-03-17 17:59:59");
//        event.setRankId(10);
//        event.setActId(ACT_ID);
//        event.setSeq("202502211");
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        UserEnterTemplateEvent event = new UserEnterTemplateEvent();
        event.setUid(50013181);
        headlineTimeComponent.onUserEnterTemplate(event,attr);
    }

    @Test
    public void otherTest() throws InterruptedException {
//        RankingTimeEnd event = new RankingTimeEnd();
//        event.setEndTime("2025-03-17 17:59:59");
//        event.setRankId(10);
//        event.setActId(ACT_ID);
//        event.setSeq("202502211");
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
//        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
//        UserEnterTemplateEvent event = new UserEnterTemplateEvent();
//        event.setUid(50013181);
//        var rsp =  puzzleComponent.awardLeft(ACT_ID,500);

//        LatestCpComponentAttr attr = latestCpComponent.getUniqueComponentAttr(ACT_ID);
//        RankingScoreChanged event = new RankingScoreChanged();
//        event.setTimestamp("2025-05-18 20:11:59");
//        event.setOccurTime("2025-05-18 20:11:59");
//        event.setRankId(18);
//        event.setActId(ACT_ID);
//        event.setPhaseId(100);
//        Map<Long, String>  actors = new HashMap<>();
//        actors.put(50059L, "87814665_2610782004");
//        event.setActors(actors);
//        event.setTimeKey(2);
//        event.setMember("50013181|1080293145");
//        event.setSeq("20250313112");
//        latestCpComponent.onRankingScoreChanged(event,attr);
        var attr = headlineTimeComponent.getUniqueComponentAttr(ACT_ID);
        AwardAttrConfig awardConfig = attr.getCpTaskPackageReward().get(100000L);
        log.info("awardConfig:{} {}",awardConfig,attr.getExcludeDanmuChannel());
        ChannelInfoVo channelInfoVo = new ChannelInfoVo();
        channelInfoVo.setSid(87814665);
        channelInfoVo.setSsid(87814665);
        String preHourTimeCode = "2025062216";
        headlineTimeComponent.settleHourRankAwardBro(attr,2285839582L,50013181L,100000,
                awardConfig,channelInfoVo,preHourTimeCode);

        Thread.sleep(12000);

    }

}
