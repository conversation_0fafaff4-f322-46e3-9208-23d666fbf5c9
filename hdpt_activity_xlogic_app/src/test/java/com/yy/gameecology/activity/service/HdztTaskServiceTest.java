package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-01-13 17:07
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties", "classpath:env/local/application-inner.properties"})
public class HdztTaskServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private HdztTaskService hdztTaskService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Test
    public void getUserTaskTest(){
        hdztTaskService.getUserTaskInfo(123456L,2021022001L,1L,1L,"");
    }


    @Test
    public void test1() {
        System.out.println(hdztRankingThriftClient.queryRankingTaskItem(2023111001L, 18, 10));
    }

    @Test
    public void test2() {
        System.out.println(hdztRankingThriftClient.queryUserTaskInfo(2023111001L, 18L, 10L, StringUtil.EMPTY, "87814665_2808628643"));
    }
}
