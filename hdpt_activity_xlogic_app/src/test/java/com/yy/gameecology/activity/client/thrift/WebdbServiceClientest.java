package com.yy.gameecology.activity.client.thrift;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbSinfoClient;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-08-25 16:23
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"
, "classpath:env/local/application-inner.properties"})
public class WebdbServiceClientest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "2");
    }


    @Autowired
    private WebdbServiceClient webdbServiceClient;

    @Autowired
    private WebdbSinfoClient webdbSinfoClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Test
    public void testCache1() {
        int loops = 100;
        for (int i = 0; i <= loops; i++) {
            //yy://pd-[sid=1353441873&subid=2767645110]

            MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, RoleType.ANCHOR, "2158001058");

        }
    }

    @Test
    public void testChannelInfoTest(){
        webdbSinfoClient.getChannelInfo(28208591);
    }

    @Test
    public void testCache() {
        int loops = 100;
        for (int i = 0; i <= loops; i++) {
            //yy://pd-[sid=1353441873&subid=2767645110]
            String template = webdbSinfoClient.getSessionTemplate(1353441873L, 2767645110L);
            MemberInfo memberInfo = memberInfoService.getMemberInfo(0L, RoleType.HALL, "1353441873_2767645110");
            memberInfoService.getMemberInfo(0L, RoleType.HALL, "1353441873_1353441873");
        }
    }

    @Test
    public void testBatchGetSubChannelInfo() {
        List<String> sidSsid = Lists.newArrayList();
        sidSsid.add("45388145_39626143");
        sidSsid.add("28780061_28780061");
        sidSsid.add("67071592_1705927104");
        Map<String, WebdbSubChannelInfo> subChannelMap = webdbServiceClient.batchGetSubChannelInfo(sidSsid);
        log.info("result :{}", subChannelMap);


    }

    @Test
    public void exportSubChannelData() {
        File fileSource = new File("D:\\tmp\\ssid.txt");


        try {
            InputStreamReader reader = new InputStreamReader(new FileInputStream(fileSource));
            BufferedReader br = new BufferedReader(reader);
            String line = br.readLine();
            StringBuilder source = new StringBuilder();
            List<String> ssid = Lists.newArrayList();
            while (line != null) {
                String[] ssidArray = line.split("\t");
                ssid.add(ssidArray[0].trim() + "_" + ssidArray[1].trim());
                line = br.readLine();
            }


            Map<String, WebdbSubChannelInfo> subChannelMap = Maps.newHashMap();

            List<List<String>> ssids = MyListUtils.subList(ssid, 300);
            for (List<String> listItem : ssids) {
                Map<String, WebdbSubChannelInfo> itemMap = webdbServiceClient.batchGetSubChannelInfo(listItem);
                subChannelMap.putAll(itemMap);
            }

            StringBuilder outPut = new StringBuilder();
            for (String sid : ssid) {
                outPut.append(sid);
                outPut.append(",");
                outPut.append(Optional.ofNullable(subChannelMap.get(sid)).map(WebdbSubChannelInfo::getName).orElse(""));
                outPut.append("\r\n");
            }


            //写入文件
            File writename = new File("D:\\tmp\\ssid_out.txt");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(outPut.toString());


            reader.close();
            out.flush();
            out.close();


        } catch (Exception e) {

        }
    }


    @Test
    public void exportChannelData() {
        File fileSource = new File("D:\\tmp\\sid.txt");


        try {
            InputStreamReader reader = new InputStreamReader(new FileInputStream(fileSource));
            BufferedReader br = new BufferedReader(reader);
            String line = br.readLine();
            StringBuilder source = new StringBuilder();
            List<String> sids = Lists.newArrayList();
            while (line != null) {
                sids.add(line.trim());
                line = br.readLine();
            }


            StringBuilder outPut = new StringBuilder();
            Map<String, ChannelBaseInfo> channelBaseInfoMap = Maps.newHashMap();
            for (String sid : sids) {
                ChannelBaseInfo channelBaseInfo = channelBaseInfoMap.get(sid);
                if (channelBaseInfo == null) {
                    channelBaseInfo = commonService.getChannelInfo(Convert.toLong(sid), false);
                }
                if (channelBaseInfo != null) {
                    channelBaseInfoMap.put(sid, channelBaseInfo);
                }

                if (channelBaseInfo == null || channelBaseInfo.getAsid() == 0) {
                    outPut.append(sid);
                } else {
                    outPut.append(channelBaseInfo.getAsid());
                }
                outPut.append("\r\n");
            }


            //写入文件
            File writename = new File("D:\\tmp\\asid_output.txt");
            // 创建新文件
            writename.createNewFile();
            BufferedWriter out = new BufferedWriter(new FileWriter(writename));
            out.write(outPut.toString());


            reader.close();
            out.flush();
            out.close();


        } catch (Exception e) {

        }
    }
}


