package com.yy.gameecology.y2025.act2025052001;

import com.yy.gameecology.activity.bean.hdzt.WelfareValuePair;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class Act2025052001ComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "礼物ID", remark = "多个使用英文逗号隔开", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, labelText = "礼物ID")
    })
    private List<String> giftIds;

    @ComponentAttrField(labelText = "抽奖奖池id")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "超次元总数")
    private long packageLimit;

    @ComponentAttrField(labelText = "超次元奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "超次元奖包id")
    private long packageId;

    @ComponentAttrField(labelText = "超次元交友奖包id")
    private long packageJyId;

    @ComponentAttrField(labelText = "发奖奖池id")
    private long backUpTaskId;

    @ComponentAttrField(labelText = "兜底奖包id")
    private long backUpPackageId;

    @ComponentAttrField(labelText = "兜底交友奖包id")
    private long backUpJyPackageId;

    @ComponentAttrField(labelText = "老用户奖包id")
    private long oldUserPackageId;

    @ComponentAttrField(labelText = "老用户交友奖包id")
    private long oldUserJyPackageId;

    @ComponentAttrField(labelText = "超次元交友包裹机会奖包id")
    private long deliveryJyPackageId;

    @ComponentAttrField(labelText = "超次元语音房包裹机会奖包id")
    private long deliveryPackageId;

    @ComponentAttrField(labelText = "抽奖--->发奖映射",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "抽奖packageId"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "业务Id"),
                    @SubField(fieldName = Constant.VALUE, type = WelfareValuePair.class, labelText = "发奖相关配置", remark = "发奖Id大于0时有效")
            })
    private Map<Long, Map<Long, WelfareValuePair>> lotteryPackageIdMap = Collections.emptyMap();

    @ComponentAttrField(labelText = "主持角色")
    private String anchorActor;

    @ComponentAttrField(labelText = "神豪角色")
    private String userActor;

    @ComponentAttrField(labelText = "业务", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "新用户判定时间差")
    private long timeDiff;

    @ComponentAttrField(labelText = "榜单id列表", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> rankIds;

    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "风控策略编码")
    private String riskStrategyKey ="MASTER_SKIN_ACT";

    @ComponentAttrField(labelText = "新老用户判断时间偏移（毫秒）")
    private long newUserTimeDiff = DateUtil.ONE_DAY_MILL_SECONDS ;
}
