package com.yy.gameecology.y2025.act2025052001;

import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryWhiteList;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryWhiteListDao;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-05-27 14:50
 **/
@Service
public class Act2025052001NewUserService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CmptYoDeliveryWhiteListDao cmptYoDeliveryWhiteListDao;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    public static class NewUserState {
        /**
         * 不在白名单内
         */
        public static final int NOT_IN_LIST = -1;

        /**
         * 不明确，初始化状态
         */
        public static final int INIT = 0;

        /**
         * 新用户
         */
        public static final int NEW_USER = 1;

        /**
         * 老用户
         */

        public static final int OLD_USER = 2;


    }

    /**
     * @param uid        uid
     * @param hdid       设备号
     * @param app        风控用 yomi、zhuiwan、pcyy
     * @param clientType 风控用  0-未知 1 -> Platform.PLATFORM_ANDROID  2 -> Platform.PLATFORM_IOS  3 -> Platform.PLATFORM_PC 4 -> Platform.PLATFORM_H5
     * @return 新老用户状态
     * @see NewUserState
     */
    public int getOrSaveNewUser(Act2025052001ComponentAttr attr, long uid, String hdid, String app, int clientType) {
        log.info("getOrInsertNewUser begin, uid:{}, hdid:{}", uid, hdid);

        long actId = attr.getActId();
        long cmptIndex = attr.getCmptUseInx();
        CmptYoDeliveryWhiteList userInfo = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(actId, cmptIndex, uid);
        if (userInfo == null) {
            return NewUserState.NOT_IN_LIST;
        }
        int curUserState = Convert.toInt(userInfo.getNewUser(), 0);
        if (curUserState > 0) {
            return curUserState;
        }

        //风控判断
        int setUserState = getUserStateByRiskCheck(attr, uid, hdid, app, clientType);

        //设备号占用判断
        if (setUserState == 0 && StringUtil.isNotBlank(hdid)) {
            setUserState = getUserStateByHdIdIsUsed(attr, uid, hdid, app, clientType);
        }

        //登陆时间 新老用户判断
        if (setUserState == 0) {
            setUserState = getUserStateByFirstLoginTime(attr, uid, hdid, app, clientType);
        }

        if (setUserState > 0) {
            //保存新老用户状态


        } else {
            //信息不足，无法判断新老用户

        }
        return setUserState;
    }


    private int getUserStateByRiskCheck(Act2025052001ComponentAttr attr, long uid, String hdid, String app, int clientType) {
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.toClient(hdid, "", app, clientType, "", "");
        boolean risk = hitRisk(uid, attr.getRiskStrategyKey(), client);
        if (risk) {
            log.warn("getOrInsertNewUser hit risk,actId:{},uid:{}, hdid:{}", attr.getActId(), uid, hdid);
            return NewUserState.OLD_USER;
        }

        return NewUserState.INIT;
    }

    private int getUserStateByHdIdIsUsed(Act2025052001ComponentAttr attr, long uid, String hdid, String app, int clientType) {
        CmptYoDeliveryWhiteList hdUserInfo = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(attr.getActId(), attr.getCmptUseInx(), hdid);
        if (hdUserInfo != null && !hdUserInfo.getHdid().equals(hdid)) {
            //设备号被被人占用
            log.warn("getOrInsertNewUser hdid used,actId:{},uid:{}, hdid:{}", attr.getActId(), uid, hdid);
            return NewUserState.OLD_USER;
        }
        return NewUserState.INIT;
    }

    private int getUserStateByFirstLoginTime(Act2025052001ComponentAttr attr, long uid, String hdid, String app, int clientType) {
        LoginRecord.LoginRecordReq req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setHdid(hdid)
                .setApp(ZhuiyaPbCommon.App.APP_YOMI).build();
        if (StringUtil.isEmpty(hdid)) {
            req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setUseLastHdid(true)
                    .setApp(ZhuiyaPbCommon.App.APP_YOMI).build();
        }
        LoginRecord.LoginRecordRsp loginRecordRsp = loginClient.queryLoginRecord(req);
        log.info("isNewUser info @req:{},rsp:{}", JsonUtils.serialize(req), JsonUtils.serialize(loginRecordRsp));
        if (loginRecordRsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(500, "系统异常，请稍后重试");
        }
        LoginRecord.LoginRecordVo loginInfo = loginRecordRsp.getResult();
        //无设备号，但uid已经是老用户
        if (StringUtil.isEmpty(hdid) && !isNewUser(attr, loginInfo.getUidFirstLoginTime())) {
            log.info("getUserStateByFirstLoginTime oldUser,uid:{},login time:{}", uid, loginInfo.getUidFirstLoginTime());
            return NewUserState.OLD_USER;
        }

        if (StringUtil.isNotBlank(hdid)) {
            long minLoginTime = Math.min(loginInfo.getUidFirstLoginTime(), loginInfo.getDeviceFirstLoginTime());
            boolean isNewUser = isNewUser(attr, minLoginTime);
            int newUserState = isNewUser ? NewUserState.NEW_USER : NewUserState.OLD_USER;
            log.info("getUserStateByFirstLoginTime,uid:{},newUserState:{},login time:{}", uid, newUserState, minLoginTime);
            return newUserState;
        }

        //无 hdid,不明确
        return NewUserState.INIT;

    }

    /**
     * 是否高风险用户
     */
    private boolean hitRisk(long uid, String riskStrategyKey, ZhuiyaPbCommon.Client client) {

        if (StringUtil.isBlank(riskStrategyKey)) {
            return false;
        }
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(ZhuiyaClientUtils.toRiskClient(client))
                .build();

        ZhuiyaRisk.RiskRsp riskRsp = zhuiwanRiskClient.getReadProxy().riskCheck(riskReq);
        if (riskRsp.getCode() != NumberUtils.INTEGER_ZERO) {
            log.warn("risk error@uid:{}{}", uid, riskRsp.getMessage());
            throw new BusinessException(500, "系统异常，请稍后重试");
        }
        ZhuiyaRisk.RiskResult riskResult = riskRsp.getRiskResult();
        return riskResult == ZhuiyaRisk.RiskResult.RISK_RESULT_FORBID;
    }

    public boolean isNewUser(Act2025052001ComponentAttr attr, long time) {
        long timeDiff = System.currentTimeMillis() - time;
        return time == 0 || timeDiff <= attr.getNewUserTimeDiff();
    }


}
