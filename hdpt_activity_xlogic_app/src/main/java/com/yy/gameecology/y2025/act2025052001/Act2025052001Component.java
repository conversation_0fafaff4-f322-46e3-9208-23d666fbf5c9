package com.yy.gameecology.y2025.act2025052001;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.event.AppPopUpEvent;
import com.yy.gameecology.activity.bean.event.JyLayerPushEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.CulClient;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataList;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryAccount;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryRecList;
import com.yy.gameecology.common.db.model.gameecology.cmpt.CmptYoDeliveryWhiteList;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.element.component.attr.YOMedalComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryAccountDao;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryRecListDao;
import com.yy.gameecology.hdzj.element.component.dao.CmptYoDeliveryWhiteListDao;
import com.yy.gameecology.hdzj.element.component.service.CmptYoDeliveryService;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.PackageLeftQuota;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.turnover.TAppId;
import com.yy.thrift.turnover.TContract;
import lombok.Data;
import org.apache.commons.codec.binary.Base64;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

@RequestMapping("/act2025052001")
@RestController
@Component
public class Act2025052001Component extends BaseActComponent<Act2025052001ComponentAttr> {

    @Autowired
    private CulClient culClient;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private SignedService signedService;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private CmptYoDeliveryService cmptYoDeliveryService;

    @Autowired
    private CmptYoDeliveryRecListDao cmptYoDeliveryRecListDao;

    @Autowired
    private CmptYoDeliveryAccountDao cmptYoDeliveryAccountDao;

    @Autowired
    private CmptYoDeliveryWhiteListDao cmptYoDeliveryWhiteListDao;

    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private Act2025052001NewUserService act2025052001NewUserService;

    public final static int SKILL_CARD_TP = 0;

    public final static int FRIEND_TP = 1;

    public final static int SKILL_CARD_INX = 1;

    public final static int FRIEND_INX = 2;

    public final static int PC_SCENE = 1;

    public final static int APP_SCENE = 2;

    public final static int ANCHOR_LAYER = 0;

    public final static int USER_LAYER = 1;

    public final static String ENTER_CHANNEL_SET = "enter_channel_set_%s";

    public final static String HANDLE_USER_INX = "handle_user_inx";

    public final static String YO_DELIVERY_LOGIN = "seq:yo_delivery_login:";

    public final static String REC_YO_DELIVERY_FIRST_SEQ = "seq:rec_yo_delivery_first:";

    public final static String REC_PACKAGE_NOTICE = "rec_package_notice";

    public final static String YO_DELIVERY_FIRST_SEQ = "seq:yo_delivery_first:";

    public final static String PACKAGE_NOTICE_PC = "package_notice_pc";

    public final static String PACKAGE_NOTICE_APP = "package_notice_app";

    private final static String WHITE_LIST_LOCK = "white_list_lock:%s";

    private final static String YO_DELIVERY_LIMIT = "YO_DELIVERY_LIMIT";

    @Override
    public Long getComponentId() {
        return C2025052001.ACT_ID;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void processPhaseTimeEnd(PhaseTimeEnd event, Act2025052001ComponentAttr attr) throws IOException {
        if (!attr.getRankIds().contains(event.getRankId()) || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        List<ChannelInfo> channelInfos = commonService.getSkillCardAllChannel();
        channelInfos.addAll(commonService.queryOnlineChannel(Template.Jiaoyou));
        long nowtime = System.currentTimeMillis();
        for (ChannelInfo channelInfo : channelInfos) {
            JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
            jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
            jyLayerPushEvent.setProducerTime(nowtime / 1000);
            jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
            jyLayerPushEvent.setFromService(attr.getActId() + "-bro");
            jyLayerPushEvent.setFromIP(SystemUtil.getIp());
            jyLayerPushEvent.setSid(channelInfo.getSid());
            jyLayerPushEvent.setSsid(channelInfo.getSsid());
            jyLayerPushEvent.setStatus(2); //1 -打开 2 -关闭
            jyLayerPushEvent.setActivityID(2025052001L); //2025052001 用来做这个活动id标识

            threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                }
            });
        }
    }

    @GetMapping("/clear")
    public Response<?> clear(@RequestParam(value = "actId", defaultValue = "0") long actId,
                             @RequestParam(value = "uid", defaultValue = "0") long uid) {
        if (SysEvHelper.isDev() || commonService.isGrey(actId)) {
            Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return Response.fail(400, "Invalid actId!");
            }
            cmptYoDeliveryAccountDao.deleteAccount(actId, attr.getCmptUseInx(), uid);
            cmptYoDeliveryWhiteListDao.updateWhiteList(actId, attr.getCmptUseInx(), uid, 0, 0, "");
            commonDataDao.hashValueDel(actId, attr.getCmptId(), attr.getCmptUseInx(), HANDLE_USER_INX, uid+"");
        }
        return Response.ok();
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, Act2025052001ComponentAttr attr) {
        final long actId = attr.getActId(), uid = sendGiftEvent.getSendUid(),
                sid = sendGiftEvent.getSid(), ssid = sendGiftEvent.getSsid();
        final String giftId = sendGiftEvent.getGiftId();
        Set<String> giftIds = ImmutableSet.copyOf(attr.getGiftIds());
        if (!giftIds.contains(giftId)) {
            return;
        }
        log.info("onSendGiftEvent with event:{}", JsonUtil.toJson(sendGiftEvent));
        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        cmptYoDeliveryAccountDao.decrAccount(actId, attr.getCmptUseInx(), uid, inxBaseInfo.getTp(), 1);
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, Act2025052001ComponentAttr attr) {
        //给主持加包裹
        if (!attr.getRankIds().contains(event.getRankId()) || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        log.info("onRankingScoreChanged with event:{}", JsonUtil.toJson(event));
        int tp = event.getRankId() == 1 ? SKILL_CARD_TP : FRIEND_TP;
        long packageId =  event.getRankId() == 1 ? attr.getDeliveryPackageId() : attr.getDeliveryJyPackageId();
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        seq = MD5SHAUtil.getMD5(seq);
        long anchorUid = Convert.toLong(event.getActors().get(Convert.toLong(attr.getAnchorActor())));
        long sendUid = Convert.toLong(event.getActors().get(Convert.toLong(attr.getUserActor())));
        cmptYoDeliveryService.sendDelivery(anchorUid, sendUid, attr.getActId(),
                attr.getCmptUseInx(), tp, 1, seq, attr.getBackUpTaskId(), packageId);
        String broSeq = REC_YO_DELIVERY_FIRST_SEQ + anchorUid;
        unicastPackageInfo(attr, broSeq, new JSONObject().toJSONString(), anchorUid, REC_PACKAGE_NOTICE);
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLogin(ZhuiwanLoginEvent loginEvent, Act2025052001ComponentAttr attr) {
        if(!loginEvent.getApp().equals("yomi")) {
            return;
        }
        long uid = loginEvent.getUid();
        CmptYoDeliveryWhiteList whiteList = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(attr.getActId(), attr.getCmptUseInx(), uid);

        if(whiteList == null) {
            return;
        }
        HandleInxResp rsp = handleUserInx2(attr, uid, loginEvent.getHdid(), whiteList.getInx(),
                whiteList.getInx() == 0 ? 0 : InxBaseInfo.getTpByInx(whiteList.getInx()),
                loginEvent.getApp(), loginEvent.getClientType());


        if(notSaveWhiteInx(whiteList)) {
            return;
        }
        String seq = makeKey(attr, YO_DELIVERY_LOGIN + uid);
        int tp = InxBaseInfo.getTpByInx(whiteList.getInx());
        //判断是否新老用户
        if(rsp.isReward()) {
            long packageId = rsp.getPackageId();
            boolean overLimit= packageId == attr.getBackUpUserPackageId() || packageId == attr.getBackUpJyUserPackageId();
            AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
            appPopUpEvent.setUid(uid);
            String url = "zhuiwan://popupWindow/https%3A%2F%2Fhd-activity.yy.com%2Fastro%2F10000451%2FhomePagePopup%3Ftype%3D#{new}%26op%3D#{op}";
            if (overLimit) {
                cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(attr.getActId(), attr.getCmptUseInx(), uid, tp);
                url = url.replace("#{new}", "2");
            } else {
                url = url.replace("#{new}", rsp.isNewUser() ? "0" : "3");
            }
            String op = whiteList.getInx() == SKILL_CARD_INX ? "0" : "1";
            url = url.replace("#{op}", op);

            appPopUpEvent.setPopUrl(url);
            if (SysEvHelper.isDev()) {
                appPopUpEvent.setSeq(makeKey(attr, "appSharePop_" + MD5SHAUtil.getMD5(seq + System.currentTimeMillis() / 1000)));
            } else {
                appPopUpEvent.setSeq(makeKey(attr, "appSharePop_" + seq));
            }
            appPopUpEvent.setProductTime(System.currentTimeMillis());
            kafkaService.sendAppPopUp(appPopUpEvent);
            log.info("onActShareSuccessEvent done,uid:{},url:{}", uid, url);
        }
    }

    public boolean pass(UserEnterTemplateEvent event, Act2025052001ComponentAttr attr, long uid, CmptYoDeliveryWhiteList whiteList, int inx) {
        if(whiteList == null || whiteList.getInx() == -1) {
            log.info("uid:{} is not in white list cant pass, whiteList:{}", uid, JsonUtil.toJson(whiteList));
            return true;
        }
        if(whiteList.getInx() > 0 && whiteList.getInx() != inx) {
            log.info("uid:{},curInx:{},inx:{}", uid, whiteList, inx);
            return true;
        }
        if(!StringUtil.isEmpty(event.getHdid()) && event.getHost().equals("yomi")) {
            log.info("uid:{}, host:{} app enter cant pass", uid, event.getHost());
            return false;
        } else {
            String extJson = event.getExtJson();
            boolean canPass = StringUtil.isNotBlank(extJson) && extJson.contains("pc") && extJson.contains(String.valueOf(attr.getActId()));
            log.info("uid:{} pc enter can pass:{}", uid, canPass);
            return !canPass;
        }
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, Act2025052001ComponentAttr attr) {

        //非yomi不处理
        long uid = event.getUid(), sid = event.getSid(), ssid = event.getSsid();
        if(!StringUtil.isEmpty(event.getHdid()) && !event.getHost().equals("yomi")) {
            return;
        }

        //用户 进频道事件 无论apc 还是 app都要给他 分赛道
        String extJson = event.getExtJson();
        boolean pcEnter = StringUtil.isNotBlank(extJson) && extJson.contains("pc")
                && extJson.contains(String.valueOf(attr.getActId()));
        boolean appEnter = !StringUtil.isEmpty(event.getHdid()) && event.getHost().equals("yomi");
        boolean full = commonDataDao.valueGet(attr.getActId(),
                attr.getCmptId(), attr.getCmptUseInx(), YO_DELIVERY_LIMIT) == attr.getPackageLimit();
        log.info("onUserEnterTemplate event:{},uid:{},actId:{},extJson:{},sid:{}" +
                "appEnter:{}, pcEnter:{}", JsonUtil.toJson(event), uid, attr.getActId(), extJson, sid, appEnter, pcEnter);
        CmptYoDeliveryWhiteList whiteList = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(attr.getActId(), attr.getCmptUseInx(), uid);
        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        int tp = inxBaseInfo.getTp();
        int inx = inxBaseInfo.getInx();

        String seq = "YO_DELIVERY:" + uid;
        boolean isAnchor = isAnchor(uid, tp);
        anchorPcBro(attr, uid, pcEnter, isAnchor, full, tp);
        CmptYoDeliveryAccount account = null;
        if(whiteList != null) {
            account =
                    cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(attr.getActId(), attr.getCmptUseInx(), uid, whiteList.getInx() == 1 ? 0 : 1);
            if (account != null && appEnter) {
                unicastPackageInfo(attr, seq + "app", new JSONObject().toJSONString(), uid, PACKAGE_NOTICE_APP);
            }
        }

        firstPcBro(attr, uid, pcEnter, isAnchor, whiteList, account, full, tp, inx);
        if(pass(event, attr, uid, whiteList, inx)) {
            return;
        }
        log.info("uid:{} is going to spread inxBaseInfo:{}", uid, JsonUtil.toJson(inxBaseInfo));
        handleUserInx2(attr, uid, event.getHdid(), inx, tp, "", 0);

        full = commonDataDao.valueGet(attr.getActId(),
                attr.getCmptId(), attr.getCmptUseInx(), YO_DELIVERY_LIMIT) == attr.getPackageLimit();
        whiteList = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(attr.getActId(), attr.getCmptUseInx(), uid);
        account =
                cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(attr.getActId(), attr.getCmptUseInx(), uid, whiteList.getInx() == 1 ? 0 : 1);
        if(whiteList.getInx() == inx && whiteList.getNewUser() != Act2025052001NewUserService.NewUserState.OLD_USER) {
            log.info("record channel user , uid:{}, sid:{}, ssid:{}, whiteList:{}, inx:{}", uid, sid, ssid, whiteList.getInx(), inx);
            commonDataDao.hashValueReplaceSet(attr.getActId(),
                    attr.getCmptId(), attr.getCmptUseInx(), String.format(ENTER_CHANNEL_SET, sid + "_" + ssid), uid + "", "1");
        }
        firstPcBro(attr, uid, pcEnter, isAnchor, whiteList, account, full, tp, inx);
        if(account != null && appEnter) {
            unicastPackageInfo(attr, seq+"app", new JSONObject().toJSONString(), uid, PACKAGE_NOTICE_APP);
        }

        broChannel(attr, sid, ssid, account, uid, whiteList);
    }

    public HandleInxResp handleUserInx2(Act2025052001ComponentAttr attr, long uid, String hdid,
                              int whiteListInx, int tp, String app, int clientType) {
        hdid = StringUtil.isBlank(hdid) ? "" : hdid;
        int newUserState = act2025052001NewUserService.getOrSaveNewUser(attr, uid, hdid, app, clientType);
        if(whiteListInx == 0) {
            log.info("handleUserInx,whiteListInx is not known,uid:{}", uid);
            return new HandleInxResp(0, false);
        }
        CmptYoDeliveryAccount account =
                cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(attr.getActId(), attr.getCmptUseInx(), uid, whiteListInx);
        if (account != null) {
            log.info("handleUserInx,delivery already done,uid:{}", uid);
            return new HandleInxResp(0, false);
        }

        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(),
                HANDLE_USER_INX, uid+"");
        if(!StringUtil.isEmpty(value)) {
            return new HandleInxResp(0, false);
        }
        log.info("handleUserInx begin,uid:{},inx:{},tp:{},app:{},clientType:{}", uid, whiteListInx, tp, app, clientType);
        //保存赛道信息
        int saveInxRet = cmptYoDeliveryWhiteListDao.updateInxWhenInxZero(attr.getActId(), attr.getCmptUseInx(), uid, whiteListInx);
        log.info("handleUserInx save inx,uid:{},inx:{},tp:{},app:{},clientType:{},saveInxRet:{}", uid, whiteListInx, tp, app, clientType, saveInxRet);

        String welfareSeq = makeKey(attr, "releaseUserAward:" + uid);
        welfareSeq = MD5SHAUtil.getMD5(welfareSeq);
        if (SysEvHelper.isDev()) {
            welfareSeq = "releaseUserAward:" + uid;
        }

        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        if (newUserState == Act2025052001NewUserService.NewUserState.NEW_USER) {
            long packageId = welfareNew(attr, tp, uid, welfareSeq, time);
            commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), HANDLE_USER_INX, uid+"", uid+"");
            return new HandleInxResp(0, true, packageId, true);
        } else if (newUserState == Act2025052001NewUserService.NewUserState.OLD_USER) {
            long packageId = welfareOld(attr, welfareSeq, tp, uid, time);
            commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), HANDLE_USER_INX, uid+"", uid+"");
            return new HandleInxResp(1, true, packageId, false);
        }
        return new HandleInxResp(0, false);
    }

    public long welfareOld(Act2025052001ComponentAttr attr, String wealfeSeq, int tp, long uid, String time) {
        long packageId = tp == SKILL_CARD_TP ? attr.getOldUserPackageId() : attr.getOldUserJyPackageId();
        Map<Long, Integer> packages = new HashMap<>();
        packages.put(packageId, 1);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), uid,
                attr.getBackUpTaskId(), ImmutableMap.of(attr.getBackUpTaskId(), packages), wealfeSeq, new HashMap<>());
        return packageId;
    }

    public long welfareNew(Act2025052001ComponentAttr attr, int tp,
                           long uid, String wealfeSeq, String time) {
        CommonDataDao.ValueIncResult valueIncResult = commonDataDao.valueIncrIgnoreWithLimit(attr.getActId(),
                attr.getCmptId(), (int) attr.getCmptUseInx(), wealfeSeq, YO_DELIVERY_LIMIT, 1, attr.getPackageLimit());
        long taskId = attr.getTaskId();
        long packageId = tp == SKILL_CARD_TP ? attr.getPackageId() : attr.getPackageJyId();
        if (valueIncResult.isViolateLimit()) {
            packageId = tp == SKILL_CARD_TP  ? attr.getBackUpUserPackageId() : attr.getBackUpJyUserPackageId();
            taskId = attr.getBackUpTaskId();
        } else {
            cmptYoDeliveryAccountDao.incrAccount(attr.getActId(), attr.getCmptUseInx(), uid, tp, 1);
        }
        Map<Long, Integer> packages = new HashMap<>();
        packages.put(packageId, 1);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), uid,
                taskId, ImmutableMap.of(taskId, packages), wealfeSeq, new HashMap<>());
        return packageId;
    }

    public void anchorPcBro(Act2025052001ComponentAttr attr, long uid, boolean pcEnter,
                           boolean isAnchor, boolean full, int tp) {
        log.info("anchorPcBro, uid:{}, pcEnter:{}, isAnchor:{}, full:{}, tp:{}",
                uid, pcEnter, isAnchor, full, tp);
        String seq = YO_DELIVERY_FIRST_SEQ + uid + ":" + tp;
        if(full) {
            return;
        }
        //pc进入才弹
        if(pcEnter && isAnchor) {
            JSONObject json = new JSONObject(1);
            json.put("tp", ANCHOR_LAYER );
            unicastPackageInfo(attr, seq, json.toJSONString(), uid, PACKAGE_NOTICE_PC);
        }
    }
    public void firstPcBro(Act2025052001ComponentAttr attr, long uid, boolean pcEnter,
                           boolean isAnchor, CmptYoDeliveryWhiteList cmptYoDeliveryWhiteList,
                           CmptYoDeliveryAccount account, boolean full, int tp, int inx) {
        log.info("firstPcBro, uid:{}, pcEnter:{}, isAnchor:{}, cmptYoDeliveryWhiteList:{}, full:{}, " +
                        "tp:{}, inx:{}",
                uid, pcEnter, isAnchor, JsonUtil.toJson(cmptYoDeliveryWhiteList), full, tp, inx);
        String seq = YO_DELIVERY_FIRST_SEQ + uid + ":" + tp;
        if(full) {
            return;
        }
        if(cmptYoDeliveryWhiteList == null) {
            return;
        }
        if(account != null && account.getCnt() == 0) {
            return;
        }
        //pc进入才弹
        if(pcEnter && !isAnchor) {
            //等分了赛道再发
            if(cmptYoDeliveryWhiteList.getInx() > 0  && cmptYoDeliveryWhiteList.getInx() == inx
                    && cmptYoDeliveryWhiteList.getNewUser() != Act2025052001NewUserService.NewUserState.OLD_USER) {
                JSONObject json = new JSONObject(1);
                json.put("tp",  USER_LAYER);
                unicastPackageInfo(attr, seq, json.toJSONString(), uid, PACKAGE_NOTICE_PC);
            }
        }
    }

    public boolean broChannel(Act2025052001ComponentAttr attr, long sid, long ssid, CmptYoDeliveryAccount account,
                           long uid, CmptYoDeliveryWhiteList whiteList) {
        //账户有钱，或者不明确的身份
        if((account != null && account.getCnt() > 0) || isFirstPcUser(whiteList)) {
            List<Long> uids = new ArrayList<>();
            uids.add(uid);
            Map<Long, WebdbUserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo(uids);
            Map<String, Object> extMap = new HashMap<>();
            if(userInfoMap.containsKey(uid)) {
                extMap.put("userLogo", userInfoMap.get(uid).getAvatar());
                extMap.put("userNick", Base64.encodeBase64String(userInfoMap.get(uid).getNick().getBytes()));
            }
            broChannel(attr, uid, sid, ssid, extMap);
            return true;
        }
        return false;
    }

    public void unicastPackageInfo(Act2025052001ComponentAttr attr, String seq, String json, long uid, String noticeType) {
        SysEvHelper.waiting(1000);
        boolean set = commonDataDao.hashValueSetNX(attr.getActId(),
                attr.getCmptId(), attr.getCmptUseInx(), noticeType, seq, uid+"");
        if(set) {
            GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                    .setActId(attr.getActId())
                    .setNoticeType(noticeType)
                    .setExtJson(json);
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                    .setCommonNoticeResponse(panel).build();
            svcSDKService.unicastUid(uid, msg);
        }
    }

    public void broChannel(Act2025052001ComponentAttr attr, long uid, long sid, long ssid, Map<String, Object> extMap) {
        SysEvHelper.waiting(1000);
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId()).setUserUid(uid).setUserNick("").setUserLogo("")
                .setBannerId(attr.getActId()).setBannerType(0)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
        broadCastHelpService.broadcast(attr.getActId(), busiId, 2, sid, ssid, bannerBroMsg);
    }

    @GetMapping("userInfo")
    public Response<UserInfoRsp> userInfo(HttpServletRequest req, HttpServletResponse resp,
                                         @RequestParam(value = "actId", defaultValue = "0") long actId,
                                          @RequestParam(value = "sid", defaultValue = "0") long sid,
                                          @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        if(sid != 0 && ssid != 0){
            //区分业务
            long busiId = webdbThriftClient.getBusiIdNocahe(sid, ssid);
            int tpInx = busiId == BusiId.SKILL_CARD.getValue() ? SKILL_CARD_TP : FRIEND_TP;
            boolean anchor = isAnchor(uid, tpInx);
            if(anchor) {
                return Response.success(null);
            }
        }
        CmptYoDeliveryWhiteList cmptYoDeliveryWhiteList =
                cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(actId, attr.getCmptUseInx(), uid);
        if(cmptYoDeliveryWhiteList == null || cmptYoDeliveryWhiteList.getInx() == 0) {
            return Response.success(null);
        }
        int tp = InxBaseInfo.getTpByInx(cmptYoDeliveryWhiteList.getInx());
        CmptYoDeliveryAccount cmptYoDeliveryAccount =
                cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(actId, attr.getCmptUseInx(), uid, tp);
        List<CmptYoDeliveryRecList> sendList = cmptYoDeliveryRecListDao.getRecListV2(actId, attr.getCmptUseInx(), uid, tp);
        boolean received = cmptYoDeliveryAccount != null;
        boolean sended = cmptYoDeliveryAccount != null && cmptYoDeliveryAccount.getCnt() == 0;
        boolean open = sendList != null && !sendList.isEmpty() && sendList.get(0).getState() == 1;
        PackageInfo packageInfo = null;
        String babyNick = null;
        String avatar = null;
        if(open) {
            packageInfo = new PackageInfo();
            packageInfo.setPackageId(sendList.get(0).getPackageId());
        }
        List<Long> uids = new ArrayList<>();
        Map<Long, WebdbUserInfo> userInfoMap = new HashMap<>();
        uids.add(uid);
        if(sendList != null && !sendList.isEmpty()) {
            CmptYoDeliveryRecList cmptYoDeliveryRecList = sendList.get(0);
            long recUid = cmptYoDeliveryRecList.getUid();
            uids.add(recUid);
            userInfoMap = webdbThriftClient.batchGetUserInfo(uids);
            if(userInfoMap.containsKey(recUid)) {
                babyNick = userInfoMap.get(recUid).getNick();
                avatar = userInfoMap.get(recUid).getAvatar();
            }
        } else {
            userInfoMap = webdbThriftClient.batchGetUserInfo(uids);
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setNewUser(cmptYoDeliveryWhiteList.getNewUser() != Act2025052001NewUserService.NewUserState.OLD_USER);
        userInfo.setTp(tp);
        userInfo.setSended(sended);
        userInfo.setOpen(open);
        userInfo.setReceived(received);
        userInfo.setPackageInfo(packageInfo);
        userInfo.setBabyNick(babyNick);
        userInfo.setUserLogo(avatar);
        return Response.success(new UserInfoRsp(userInfo));
    }

    @GetMapping("awardInfo")
    public Response<AwardsRsp> award(HttpServletRequest req, HttpServletResponse resp,
                                     @RequestParam(value = "actId", defaultValue = "0") long actId,
                                     @RequestParam(value = "sid", defaultValue = "0") long sid,
                                     @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        int tp = inxBaseInfo.getTp();
        Map<Long, AwardModelInfo> awardModelInfoMap = new HashMap<>();
        Map<Long, PackageLeftQuota> countMap = new HashMap<>();
        Date now = commonService.getNow(actId);
        try {
            awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            countMap = hdztAwardServiceClient.getTaskLeftQuota(attr.getLotteryTaskId(), now);
        } catch (TException e) {
            log.error("queryTask info error");
        }
        Map<Long, AwardInfo> awardInfoMap = new HashMap<>();
        for (Long pakcageId : awardModelInfoMap.keySet()) {
            AwardInfo awardInfo = new AwardInfo();
            awardInfo.setGiftIcon(awardModelInfoMap.get(pakcageId).getPackageImage());
            awardInfo.setGiftName(awardModelInfoMap.get(pakcageId).getPackageName());
            awardInfo.setPackageId(pakcageId);
            awardInfoMap.put(pakcageId, awardInfo);
            if(countMap.containsKey(pakcageId)) {
                awardInfo.setEmpty(countMap.get(pakcageId).getTotalLeft() <= 0);
            }
        }
        try {
            awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(attr.getBackUpTaskId());
        } catch (TException e) {
            log.error("queryTask info error e:{}", e.getMessage(), e);
        }
        AwardModelInfo awardModelInfo = awardModelInfoMap.get(attr.getBackUpPackageId());
        AwardInfo awardInfo = new AwardInfo();
        awardInfo.setGiftIcon(awardModelInfo.getPackageImage());
        awardInfo.setGiftName(awardModelInfo.getPackageName());
        awardInfo.setPackageId(awardModelInfo.getPackageId());
        awardInfoMap.put(awardModelInfo.getPackageId(), awardInfo);
        AwardsRsp awardsRsp = new AwardsRsp();
        awardsRsp.setAwardInfos(awardInfoMap);
        return Response.success(awardsRsp);
    }

    @GetMapping("draw")
    public Response<DrawRsp> draw(HttpServletRequest req, HttpServletResponse resp,
                                             @RequestParam(value = "actId", defaultValue = "0") long actId,
                                  @RequestParam(value = "id", defaultValue = "0") long id) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        int status = actInfoService.actTimeStatus(actId);
        if (status > 0) {
            return Response.fail(3, "活动已结束!");
        }
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }

        CmptYoDeliveryRecList cmptYoDeliveryRecList = cmptYoDeliveryRecListDao.getRecListById(id);
        if(cmptYoDeliveryRecList == null) {
            return Response.fail(400, "参数错误");
        }
        int tp = cmptYoDeliveryRecList.getTp();
        Map<Long, AwardModelInfo> awardModelMap = new HashMap<>();
        try {
            awardModelMap = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            awardModelMap.putAll(hdztAwardServiceClient.queryAwardTasks(attr.getBackUpTaskId()));
        } catch (TException e) {
            log.error("queryTask info error:{}", e.getMessage(), e);
        }

        DrawRsp drawRsp = new DrawRsp();
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        //检查id合法性
        String seq = "lottery_" + uid + "_" + id;
        String lockName = makeKey(attr, String.format(WHITE_LIST_LOCK, uid));
        Secret lock = locker.lock(lockName, 5, System.currentTimeMillis() + "_" + uid + "_" + id, 10);
        Awards awards = new Awards();
        try {
            long packageId = 0L;
            //抽奖
            BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq,
                    BusiId.MAKE_FRIEND.getValue(), uid, attr.getLotteryTaskId(), 1, null,
                    new HashMap<>()
                    , 3);
            //如果抽出xxcy 发兜底礼物
            if (result != null && result.getCode() == LotteryException.E_NOT_HIT) {
                packageId = tp == SKILL_CARD_TP ? attr.getBackUpPackageId() : attr.getBackUpJyPackageId();
                Map<Long, Integer> packageTmps = new HashMap<>();
                packageTmps.put(packageId, 1);
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), uid, attr.getBackUpTaskId(),
                        ImmutableMap.of(attr.getBackUpTaskId(), packageTmps), seq, new HashMap<>());
                //显示用
                packageId = attr.getBackUpPackageId();
            } else if (result == null || result.getCode() != 0) {
                return Response.fail(1, "抽奖失败");
            } else {
                Map<Long, Long> recordPackageMap = result.getRecordPackages();
                for (Long recordId : recordPackageMap.keySet()) {
                    packageId = recordPackageMap.get(recordId);
                }
                if(attr.getLotteryPackageIdMap().containsKey(packageId)) {
                    long tmpPackageId = attr.getLotteryPackageIdMap().get(packageId).get((long)tp).getWelfarePackageId();
                    Map<Long, Integer> packageTmps = new HashMap<>();
                    packageTmps.put(tmpPackageId, 1);
                    hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.MAKE_FRIEND.getValue(), uid, attr.getBackUpTaskId(),
                            ImmutableMap.of(attr.getBackUpTaskId(), packageTmps), seq, new HashMap<>());
                }
            }
            awards.setPackageId(packageId);
            cmptYoDeliveryRecListDao.updateRecList(id,  1, packageId);
            AwardModelInfo awardModelInfo = awardModelMap.get(packageId);
            awards.setGiftCount(1);
            awards.setGiftName(awardModelInfo.getPackageName());
            awards.setGiftUnit(awardModelInfo.getUnit());
            awards.setGiftIcon(awardModelInfo.getPackageImage());
            awards.setSendUid(cmptYoDeliveryRecList.getSendUid());
            awards.setTime(time);
            awards.setUid(uid);
            drawRsp.setAwards(awards);
            commonDataDao.listInsertIgnore(actId, attr.getCmptId(), (int)attr.getCmptUseInx(), seq, uid+""+tp, JSON.toJSONString(awards));
            commonDataDao.listInsertIgnore(actId, attr.getCmptId(), (int)attr.getCmptUseInx(), seq, "all", JSON.toJSONString(awards));
        } catch (Exception e) {
            log.error("draw error {}", e.getMessage(), e);
        } finally {
            locker.unlock(lockName, lock, 5);
        }
        return Response.success(drawRsp);
    }

    @GetMapping("awardRecords")
    public Response<AllAwardRecordRsp> getAllAwardRecords(HttpServletRequest req, HttpServletResponse resp,
                                                      @RequestParam(value = "actId", defaultValue = "0") long actId,
                                                      @RequestParam(value = "sid", defaultValue = "0") long sid,
                                                      @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        List<ComponentDataList> componentDataLists = commonDataDao.listSelect(actId, attr.getCmptId(), (int)attr.getCmptUseInx(), "all", 0, 100);
        AllAwardRecordRsp rsp = new AllAwardRecordRsp();
        List<Long> uids = new ArrayList<>();
        List<AllAwardRecord> arwards = new ArrayList<>();
        List<Awards> awardsList = new ArrayList<>();
        for (ComponentDataList componentDataList : componentDataLists) {
            Awards awards = JsonUtil.toObject(componentDataList.getValue(), Awards.class);
            uids.add(awards.getUid());
            awardsList.add(awards);
        }
        if(!uids.isEmpty()) {
            Map<Long, WebdbUserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo(uids);
            for (Awards awards : awardsList) {
                WebdbUserInfo userInfo = userInfoMap.get(awards.getUid());
                if(userInfo == null) {
                    continue;
                }
                AllAwardRecord awardRecord = new AllAwardRecord();
                awardRecord.setPackageId(awards.getPackageId());
                awardRecord.setBabyLogo(userInfo.getAvatar());
                awardRecord.setBabyNick(userInfo.getNick());
                arwards.add(awardRecord);
            }
        }
        rsp.setList(arwards);
        return Response.success(rsp);
    }

    @GetMapping("getMyAwardRecords")
    public Response<AwardRecordRsp> getMyAwardRecords(HttpServletRequest req, HttpServletResponse resp,
                                                      @RequestParam(value = "actId", defaultValue = "0") long actId,
                                                      @RequestParam(value = "sid", defaultValue = "0") long sid,
                                                      @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
       long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        List<ComponentDataList> componentDataLists = commonDataDao.listSelect(actId, attr.getCmptId(),
                (int)attr.getCmptUseInx(), uid+""+inxBaseInfo.getTp(), 0, 100);
        AwardRecordRsp awardRecordRsp  = new AwardRecordRsp();
        List<Long> uids = new ArrayList<>();
        List<AwardRecord> arwards = new ArrayList<>();
        List<Awards> awardsList = new ArrayList<>();
        for (ComponentDataList componentDataList : componentDataLists) {
            Awards awards = JsonUtil.toObject(componentDataList.getValue(), Awards.class);
            uids.add(awards.getSendUid());
            awardsList.add(awards);
        }
        if(!uids.isEmpty()) {
            Map<Long, WebdbUserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo(uids);
            for (Awards awards : awardsList) {
                WebdbUserInfo userInfo = userInfoMap.get(awards.getSendUid());
                if(userInfo == null) {
                    continue;
                }
                AwardRecord awardRecord = new AwardRecord();
                awardRecord.setTime(awards.getTime());
                awardRecord.setAward(awards.getGiftName());
                awardRecord.setSendNick(userInfo.getNick());
                arwards.add(awardRecord);
            }
        }
        awardRecordRsp.setList(arwards);
        return Response.success(awardRecordRsp);
    }

    @GetMapping("packages")
    public Response<PackagesRsp> packages(HttpServletRequest req, HttpServletResponse resp,
                                          @RequestParam(value = "actId", defaultValue = "0") long actId,
                                          @RequestParam(value = "sid", defaultValue = "0") long sid,
                                          @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }

        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        long busiId = webdbThriftClient.getBusiIdNocahe(sid, ssid);
        int tp = inxBaseInfo.getTp();

        int status = actInfoService.actTimeStatus(actId);
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }

        boolean anchor = isAnchor(uid, tp);
        if (!anchor) {
            return Response.success(new PackagesRsp());
        }

        if (sid == 0 || ssid == 0) {
            return Response.success(new PackagesRsp());
        }

        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        List<User> users = new ArrayList<>();
        List<Package> packages = new ArrayList<>();
        try {

            Map<String, String> enterChannelSet = commonDataDao.hashGetAllWithLimit(attr.getActId(),
                    attr.getCmptId(), attr.getCmptUseInx(), String.format(ENTER_CHANNEL_SET, sid + "_" + ssid), attr.getPackageLimit());
            List<String> uidstrs = new ArrayList<>(enterChannelSet.keySet());
            List<Long> channelUids = new ArrayList<>();
            for (String s : uidstrs) {
                channelUids.add(Convert.toLong(s));
            }

            List<Long> uids = new ArrayList<>();
            List<Long> accountUids = cmptYoDeliveryAccountDao.listAccount(actId, attr.getCmptUseInx(),
                    tp == SKILL_CARD_TP ? SKILL_CARD_TP : FRIEND_TP);
            if(accountUids != null && !accountUids.isEmpty()) {
                uids.addAll(accountUids);
            }

            List<Long> unknownHdidUids = cmptYoDeliveryWhiteListDao.getWhiteListUids(actId, attr.getCmptUseInx(),
                    tp == SKILL_CARD_TP ? SKILL_CARD_INX : FRIEND_INX, channelUids);
            if(unknownHdidUids != null) {
                uids.addAll(unknownHdidUids);
            }

            List<Long> filterUids = culClient.queryUserChannel(uids, sid, ssid);
            if(filterUids != null && !filterUids.isEmpty()) {
                Map<Long, WebdbUserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo(filterUids);
                for (Long filterUid : filterUids) {
                    WebdbUserInfo userInfo = userInfoMap.get(filterUid);
                    if (userInfo == null) {
                        continue;
                    }
                    User user = new User();
                    user.setNick(userInfo.getNick());
                    user.setAvatar(WebdbUtils.getLogo(userInfo));
                    users.add(user);
                }
            }
            List<CmptYoDeliveryRecList> recLists = cmptYoDeliveryRecListDao.getRecList(actId, attr.getCmptUseInx(), uid, tp);
            if(recLists != null && !recLists.isEmpty()) {
                uids = new ArrayList<>();
                List<Long> finalUids = uids;
                recLists.forEach(recList -> {
                    finalUids.add(recList.getSendUid());
                });
                Map<Long, WebdbUserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo(finalUids);
                for (CmptYoDeliveryRecList recList : recLists) {
                    WebdbUserInfo userInfo = userInfoMap.get(recList.getSendUid());
                    if(userInfo == null) {
                        continue;
                    }
                    Package aPackage = new Package();
                    aPackage.setId(recList.getId());
                    aPackage.setNick(userInfo.getNick());
                    aPackage.setAvatar(userInfo.getAvatar());
                    aPackage.setPackageId(recList.getPackageId());
                    aPackage.setState(recList.getState());
                    packages.add(aPackage);
                }
            }
        } catch (Exception e) {
            log.error("packages error e:{}", e.getMessage(), e);
        }
        return Response.success(new PackagesRsp(packages, users, status > 0));
    }

    @GetMapping("queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(@RequestParam("seq") String seq,
                                                          @RequestParam(value = "actId", defaultValue = "0") long actId,
                                                          @RequestParam(value = "sid", defaultValue = "0") long sid,
                                                          @RequestParam(value = "ssid", defaultValue = "0") long ssid,
                                                          @RequestParam(value = "uid", defaultValue = "0") long uid) {
        if (actId == 0 || uid == 0) {
            return new Response<>(Code.E_DATA_ERROR.getCode(), "para error");
        }
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        LayerInfo layerInfo = getLayerInfo(attr, actId, attr.getCmptUseInx(), uid, sid, ssid);
        Map<String, Object> data = new HashMap<>(3);
        int showStatus = showLayerStatus(attr, actId, attr.getCmptUseInx(), layerInfo, uid);
        data.put("showStatus", showStatus);
        data.put("time", System.currentTimeMillis());
        data.put("grey", commonService.isGrey(actId));
        return Response.success(data);
    }

    private int showLayerStatus(Act2025052001ComponentAttr attr, long actId, long cmptInx, LayerInfo layerInfo, long uid) {
        final int showLayer = 1;
        final int hideLayer = 0;
        if (layerInfo == null) {
            return hideLayer;
        }
        //主持显示挂件
        if (layerInfo.getTp() == 0) {
            return showLayer;
        }

        //奖池耗光，没发有价值礼物奖品的用户不显示挂件
        long value = commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), YO_DELIVERY_LIMIT);
        boolean poolOut = value >= attr.getPackageLimit();
        if (poolOut) {
            CmptYoDeliveryWhiteList whiteList = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(actId, cmptInx, uid);
            if (whiteList != null) {
                var account = cmptYoDeliveryAccountDao.getCmptYoDeliveryAccount(attr.getActId(), attr.getCmptUseInx(), uid, whiteList.getInx() == 1 ? 0 : 1);
                if (account == null) {
                    log.info("showLayerStatus poolOut,uid:{}", uid);
                    return hideLayer;
                }
            }
        }

        return showLayer;
    }

    @GetMapping("layoutInfo")
    public Response<LayerInfoRsp> layoutInfo(HttpServletRequest req, HttpServletResponse resp,
                                             @RequestParam(value = "actId", defaultValue = "0") long actId,
                                             @RequestParam(value = "sid", defaultValue = "0") long sid,
                                             @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        Act2025052001ComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }
        LayerInfo layerInfo = getLayerInfo(attr, actId, attr.getCmptUseInx(), uid, sid, ssid);
        log.info("uid:{}, sid:{}, ssid:{}, layoutInfo:{}", uid, sid, ssid, JsonUtil.toJson(layerInfo));
        return Response.success(new LayerInfoRsp(layerInfo));
    }


    public LayerInfo getLayerInfo(Act2025052001ComponentAttr attr, long actId, long cmptInx, long uid, long sid, long ssid) {
        if (!actInfoService.inActTime(actId)) {
            return null;
        }
        InxBaseInfo inxBaseInfo = getInxBaseInfo(sid, ssid);
        int tp = inxBaseInfo.getTp();
        int inx = inxBaseInfo.getInx();
        if(tp == 0) {
            long skillcardSid = turnoverFamilyThriftClient.queryContractFamilyId(uid);
            if(skillcardSid > 0) {
                return new LayerInfo(0);
            }
        } else {
            TContract dating = turnoverServiceClient.queryContractByAnchor(uid, TAppId.Dating);
            long jySid = 0;
            if (dating != null && dating.liveUid == uid) {
                jySid =  dating.sid;
            }
            if (jySid > 0) {
                return new LayerInfo(0);
            }
        }
        CmptYoDeliveryWhiteList cmptYoDeliveryWhiteList = cmptYoDeliveryWhiteListDao.getCmptYoDeliveryWhiteList(actId, cmptInx, uid);
        if (cmptYoDeliveryWhiteList == null || cmptYoDeliveryWhiteList.getInx() == -1) {
            return null;
        }
        if(cmptYoDeliveryWhiteList.getInx() > 0 && cmptYoDeliveryWhiteList.getInx() != inx) {
            return null;
        }
        if(cmptYoDeliveryWhiteList.getNewUser() == Act2025052001NewUserService.NewUserState.OLD_USER) {
            return null;
        }
        if(cmptYoDeliveryWhiteList.getInx() == 0) {
            HandleInxResp rsp = handleUserInx2(attr, uid, "", inx, tp, "", 0);
            if(rsp.getRet() == 1) {
                return null;
            }
        }
        return new LayerInfo(1);
    }

    public boolean isAnchor(long uid, int tp) {
        if(tp == 0) {
            long skillcardSid = turnoverFamilyThriftClient.queryContractFamilyId(uid);
            return skillcardSid > 0;
        } else {
            TContract dating = turnoverServiceClient.queryContractByAnchor(uid, TAppId.Dating);
            long jySid = 0;
            if (dating != null && dating.liveUid == uid) {
                jySid =  dating.sid;
            }
            return jySid > 0;
        }
    }

    public boolean isFirstPcUser(CmptYoDeliveryWhiteList whiteList) {
        return whiteList != null && whiteList.getInx() > 0 &&
                whiteList.getNewUser() == Act2025052001NewUserService.NewUserState.INIT;
    }


    public InxBaseInfo getInxBaseInfo(long sid, long ssid) {
        long busiId = webdbThriftClient.getBusiId(sid, ssid);
        int tp = busiId == BusiId.SKILL_CARD.getValue() ? SKILL_CARD_TP : FRIEND_TP;
        int inx = tp == SKILL_CARD_TP ? SKILL_CARD_INX : FRIEND_INX;
        return new InxBaseInfo(inx, tp);
    }

    private boolean notSaveWhiteInx(CmptYoDeliveryWhiteList whiteList) {
        return whiteList == null || Convert.toInt(whiteList.getInx(), 0) == 0;
    }

    private boolean isNewUser(CmptYoDeliveryWhiteList whiteList) {
        return whiteList != null && Convert.toInt(whiteList.getNewUser(), 0) == Act2025052001NewUserService.NewUserState.NEW_USER;
    }

    public boolean isNewUser(long time) {
        long timeDiff = System.currentTimeMillis() - time;
        return timeDiff <= 24 * 60 * 60 * 1000;
    }

    @Data
    public static class HandleInxResp {
        private int ret;
        private boolean newUser;
        private boolean reward;
        private long packageId;

        public HandleInxResp() {
        }

        public HandleInxResp(int ret, boolean reward) {
            this.ret = ret;
            this.reward = reward;
        }

        public HandleInxResp(int ret, boolean reward, long packageId, boolean newUser) {
            this.ret = ret;
            this.reward = reward;
            this.packageId = packageId;
            this.newUser = newUser;
        }
    }


    @Data
    public static class InxBaseInfo {
        private int inx;
        private int tp;

        public InxBaseInfo(int inx, int tp) {
            this.inx = inx;
            this.tp = tp;
        }

        public InxBaseInfo() {
        }

        public static int getInxByTp(int tp) {
            return tp == SKILL_CARD_TP ? SKILL_CARD_INX : FRIEND_INX;
        }

        public static int getTpByInx(int inx) {
            return inx == SKILL_CARD_INX ? SKILL_CARD_TP : FRIEND_TP;
        }
    }

    @Data
    public static class UserInfoRsp {
        private UserInfo info;

        public UserInfoRsp(UserInfo info) {
            this.info = info;
        }

        public UserInfoRsp() {
        }
    }

    @Data
    public static class UserInfo {
        private int tp;
        private boolean received;
        private boolean newUser;
        private boolean sended;
        private boolean open;
        private String babyNick;
        private String userLogo;
        private PackageInfo packageInfo;
    }

    @Data
    public static class PackageInfo {
        private long packageId;
    }

    @Data
    public static class PackagesRsp {
        private boolean end;
        private List<Package> packages;
        private List<User> users;

        public PackagesRsp() {
        }

        public PackagesRsp(List<Package> packages, List<User> users, boolean end) {
            this.end = end;
            this.packages = packages;
            this.users = users;
        }
    }

    @Data
    public static class AwardsRsp {
        private Map<Long, AwardInfo> awardInfos;
    }

    @Data
    public static class AwardInfo {
        private long packageId;
        private String giftName;
        private String giftIcon;
        private boolean empty;
    }

    @Data
    public static class Package {
        private long id;
        private long packageId;
        private String nick;
        private String avatar;
        private String packageImage;
        private String packageName;
        private Integer state;
    }

    @Data
    public static class User {
        private String nick;
        private String avatar;
    }

    @Data
    public static class AwardRecordRsp {
        private List<AwardRecord> list;
    }

    @Data
    public static class AwardRecord {
        private String time;
        private String sendNick;
        private String award;
    }

    @Data
    public static class AllAwardRecordRsp {
        private List<AllAwardRecord> list;
    }

    @Data
    public static class AllAwardRecord {
        private long packageId;
        private String babyLogo;
        private String babyNick;
    }

    @Data
    public static class DrawRsp {
        private Awards awards;
    }

    @Data
    public static class Awards {
        private long giftCount;
        private String giftName;
        private String giftIcon;
        private String giftUnit;
        private long sendUid;
        private long uid;
        private long packageId;
        private String time;
    }

    @Data
    public static class LayerInfoRsp {
        private LayerInfo layerInfo;

        public LayerInfoRsp(LayerInfo layerInfo) {
            this.layerInfo = layerInfo;
        }
    }

    @Data
    public static class LayerInfo {
        public int tp; //0 用户视角 1 主持视角

        public LayerInfo(int tp) {
            this.tp = tp;
        }
    }
}
