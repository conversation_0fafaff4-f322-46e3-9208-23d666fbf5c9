package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class CmptYoDeliveryRecList implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<CmptYoDeliveryRecList> ROW_MAPPER = (rs, rowNum) -> {
        CmptYoDeliveryRecList result = new CmptYoDeliveryRecList();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUid(rs.getLong("uid"));
        result.setSendUid(rs.getLong("send_uid"));
        result.setTp(rs.getInt("tp"));
        result.setState(rs.getInt("state"));
        result.setCtime(rs.getTimestamp("ctime"));
        result.setSeq(rs.getString("seq"));
        result.setPackageId(rs.getLong("package_id"));
        return result;
    };

    private Long id;
    private Long actId;
    private Long cmptUseInx;
    private Long uid;
    private Long sendUid;
    private Integer tp;
    private Integer state; // 0 未打开 1打开
    private Date ctime;
    private String seq;
    private Long packageId;
}
