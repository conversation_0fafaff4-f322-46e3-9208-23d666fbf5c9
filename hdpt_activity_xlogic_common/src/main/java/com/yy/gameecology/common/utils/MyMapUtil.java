
package com.yy.gameecology.common.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * Title: Description: Create Time: 2014-05-28 下午9:05 author: wangyan version: 1.0
 */
public class MyMapUtil {
    public static <K, V> Map<K, V> listAsMap(Collection<V> sourceList, ListToMapConverter<K, V> converter) {
        Map<K, V> newMap = Maps.newLinkedHashMap();
        for (V item : sourceList) {
            newMap.put(converter.getKey(item), item);
        }
        return newMap;
    }

    public static interface ListToMapConverter<K, V> {
        public K getKey(V item);
    }

    /**
     * 按查询偏移量指示，提取指定范围内的热门频道
     *
     * @param data
     * @param index
     * @param fetchs
     * @return
     */
    public static <K, V> Map<K, V> getSubMap(Map<K, V> data, long index, long fetchs) {
        Map<K, V> map = Maps.newLinkedHashMap();
        if (index < data.size()) {
            int p = 0;
            int c = 0;
            for (K key : data.keySet()) {
                if (++p < index) {
                    continue;
                }
                if (++c > fetchs) {
                    break;
                }
                map.put(key, data.get(key));
            }
        }
        return map;
    }

    public static <K, V extends Comparable<? super V>> LinkedHashMap<K, V> sortByValue(Map<K, V> map, final Comparator<V> comparator) {
        List<Map.Entry<K, V>> list = Lists.newArrayList(map.entrySet());
        list.sort((o1, o2) -> comparator.compare(o1.getValue(), o2.getValue()));

        return entryListToLinkedHashMap(list);
    }

    public static <K extends Comparable<? super K>, V> LinkedHashMap<K, V> sortByKey(Map<K, V> map, final Comparator<K> comparator) {
        List<Map.Entry<K, V>> list = Lists.newArrayList(map.entrySet());
        list.sort((o1, o2) -> comparator.compare(o1.getKey(), o2.getKey()));
        return entryListToLinkedHashMap(list);
    }

    /**
     * @deprecated key肯定不会一样，这是一个没必要的方法
     * @param map
     * @param keyComparator
     * @param valueComparator
     * @return
     * @param <K>
     * @param <V>
     */
    @Deprecated
    public static <K extends Comparable<? super K>, V extends Comparable<? super V>> LinkedHashMap<K, V> sortByKeyPriorValue(Map<K, V> map, final Comparator<K> keyComparator, final Comparator<V> valueComparator) {
        List<Map.Entry<K, V>> list = Lists.newArrayList(map.entrySet());
        list.sort((o1, o2) -> valueComparator.compare(o1.getValue(), o2.getValue()));
        list.sort((o1, o2) -> keyComparator.compare(o1.getKey(), o2.getKey()));
        return entryListToLinkedHashMap(list);
    }

    /**
     * 利用collection 的stable sort 优先排序value， value相同则排序key
     *
     * @param map
     * @param keyComparator
     * @param valueComparator
     * @return
     * @param <K>
     * @param <V>
     */
    public static <K extends Comparable<? super K>, V extends Comparable<? super V>> LinkedHashMap<K, V> sortByValuePriorKey(Map<K, V> map, final Comparator<K> keyComparator, final Comparator<V> valueComparator) {
        List<Map.Entry<K, V>> list = Lists.newArrayList(map.entrySet());
        list.sort((o1, o2) -> keyComparator.compare(o1.getKey(), o2.getKey()));
        list.sort((o1, o2) -> valueComparator.compare(o1.getValue(), o2.getValue()));
        return entryListToLinkedHashMap(list);
    }

    public static <K, V> LinkedHashMap<K, V> entryListToLinkedHashMap(List<Map.Entry<K, V>> list) {
        LinkedHashMap<K, V> result = Maps.newLinkedHashMapWithExpectedSize(list.size() + 7);
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }

        return result;
    }

    public static <K, V> K getKeyByValue(Map<K, V> map, V v) {
        if (MapUtils.isEmpty(map) || v == null) {
            return null;
        }

        for (Map.Entry<K, V> entry : map.entrySet()) {
            if (v == entry.getValue()) {
                return entry.getKey();
            }
        }

        return null;
    }

    public static <K, V> Map<V, K> inverses(Map<K, V> map) {
        if (map == null) {
            return null;
        }

        Map<V, K> result = new HashMap<>(map.size());
        for (Map.Entry<K, V> entry : map.entrySet()) {
            result.put(entry.getValue(), entry.getKey());
        }

        return result;
    }

    /**
     * 返回map中最大且不大于threshold的entry
     * @param map
     * @param threshold
     * @return 目标entry，无满足条件则返回null
     * @param <K>
     * @param <V>
     */
    public static <K extends Comparable<K>, V> Map.Entry<K, V> floor(Map<K, V> map, K threshold) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        if (map instanceof NavigableMap<K,V> navigableMap) {
            return navigableMap.floorEntry(threshold);
        }

        TreeMap<K, V> treeMap = new TreeMap<>(map);
        return treeMap.floorEntry(threshold);
    }

    /**
     * 返回map中最小且不小于threshold的entry
     * @param map
     * @param threshold
     * @return 目标entry，无满足条件则返回null
     * @param <K>
     * @param <V>
     */
    public static <K extends Comparable<K>, V> Map.Entry<K, V> ceil(Map<K, V> map, K threshold) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        if (map instanceof NavigableMap<K,V> navigableMap) {
            return navigableMap.ceilingEntry(threshold);
        }

        TreeMap<K, V> treeMap = new TreeMap<>(map);
        return treeMap.ceilingEntry(threshold);
    }
}
