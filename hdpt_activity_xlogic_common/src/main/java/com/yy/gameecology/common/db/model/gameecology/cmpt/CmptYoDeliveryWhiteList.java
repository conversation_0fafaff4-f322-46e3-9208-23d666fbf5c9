package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class CmptYoDeliveryWhiteList implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String[] TABLE_PKS = new String[]{"act_id", "cmpt_use_inx", "uid"};

    public static String TABLE_NAME = "cmpt_5150_user_puzzle";

    public static final RowMapper<CmptYoDeliveryWhiteList> ROW_MAPPER = (rs, rowNum) -> {
        CmptYoDeliveryWhiteList result = new CmptYoDeliveryWhiteList();
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUid(rs.getLong("uid"));
        result.setInx(rs.getInt("inx"));
        result.setScene(rs.getInt("scene"));
        result.setNewUser(rs.getInt("new_user"));
        result.setHdid(rs.getString("hdid"));
        result.setFirstLoginTime(rs.getLong("first_login_time"));
        result.setCtime(rs.getTimestamp("ctime"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    private Long actId;
    private Long cmptUseInx;
    private Long uid;
    private Integer inx; // 0未分赛道 1语音房 2交友 -1黑名单(老用户)
    private Integer scene; // 触发场景 0初始化 1 pc 2 app
    private Integer newUser; // 0初始化 1 新用户 2 老用户
    private String hdid; // hdid
    private Long firstLoginTime; // yo最早登录时间
    private Date ctime; // 创建时间
    private Date updateTime; // 更新时间
}
