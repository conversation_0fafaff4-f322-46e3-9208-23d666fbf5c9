
package com.yy.gameecology.common.support;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * sprring 容器的ApplicationContext感知工厂 ，启动web系统，初始化spring ioc容器将被该类感知
 * <ul>不要在容器初始化的地方调用这个类的getBean方法，只能在运行时调用getBean方法</ul>
 * <ul>除非万不得已，不然不要使用这个类</ul>
 * <AUTHOR>
 * @date 2018年8月28日 下午8:27:41
 */
public class SpringBeanAwareFactory implements ApplicationContextAware {
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        SpringBeanAwareFactory.applicationContext = applicationContext;
    }

    public static ApplicationContext getBeanFactory() {
        return applicationContext;
    }

    public static Object getBean(String name) {
        if (applicationContext == null) {
            throw new java.lang.NullPointerException(SpringBeanAwareFactory.class.getName()
                    + "exception:must assign value to the property beanFactory.");
        }
        return applicationContext.getBean(name);
    }

    public static <T> T getBean(Class<T> classType) {
        if (applicationContext == null) {
            throw new java.lang.NullPointerException(SpringBeanAwareFactory.class.getName()
                    + "exception:must assign value to the property beanFactory.");
        }
        return applicationContext.getBean(classType);
    }

    public static <T> T getBean(Class<T> classType, String name) {
        if (applicationContext == null) {
            throw new java.lang.NullPointerException(SpringBeanAwareFactory.class.getName()
                    + "exception:must assign value to the property beanFactory.");
        }
        return applicationContext.getBean(classType, name);
    }
}
