/*
 * Copyright 2002-2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//package org.springframework.cache.ehcache;

package com.yy.gameecology.common.cache.ehcache;

import net.sf.ehcache.CacheException;
import net.sf.ehcache.CacheManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;

/**
 * {@link FactoryBean} that exposes an EHCache {@link net.sf.ehcache.CacheManager}
 * instance (independent or shared), configured from a specified config location.
 *
 * <p>If no config location is specified, a CacheManager will be configured from
 * "ehcache.xml" in the root of the class path (that is, default EHCache initialization
 * - as defined in the EHCache docs - will apply).
 *
 * <p>Setting up a separate EhCacheManagerFactoryBean is also advisable when using
 * EhCacheFactoryBean, as it provides a (by default) independent CacheManager instance
 * and cares for proper shutdown of the CacheManager. EhCacheManagerFactoryBean is
 * also necessary for loading EHCache configuration from a non-default config location.
 *
 * <p>Note: As of Spring 3.0, Spring's EHCache support requires EHCache 1.3 or higher.
 *
 * <AUTHOR> Kopylenko
 * <AUTHOR> Hoeller
 * @since 1.1.1
 * @see #setConfigLocation
 * @see #setShared
 * @see org.springframework.cache.ehcache.EhCacheFactoryBean
 * @see net.sf.ehcache.CacheManager
 */
@SuppressWarnings("rawtypes")
public class EhCacheManagerFactoryBean implements FactoryBean, InitializingBean, DisposableBean {

    protected final Log logger = LogFactory.getLog(getClass());

    private Resource configLocation;

    private boolean shared = false;

    @SuppressWarnings("unused")
    private String cacheManagerName;

    private CacheManager cacheManager;


    /**
     * Set the location of the EHCache config file. A typical value is "/WEB-INF/ehcache.xml".
     * <p>Default is "ehcache.xml" in the root of the class path, or if not found,
     * "ehcache-failsafe.xml" in the EHCache jar (default EHCache initialization).
     * @see net.sf.ehcache.CacheManager#create(java.io.InputStream)
     * @see net.sf.ehcache.CacheManager#CacheManager(java.io.InputStream)
     */
    public void setConfigLocation(Resource configLocation) {
        this.configLocation = configLocation;
    }

    /**
     * Set whether the EHCache CacheManager should be shared (as a singleton at the VM level)
     * or independent (typically local within the application). Default is "false", creating
     * an independent instance.
     * @see net.sf.ehcache.CacheManager#create()
     * @see net.sf.ehcache.CacheManager#CacheManager()
     */
    public void setShared(boolean shared) {
        this.shared = shared;
    }


    public void afterPropertiesSet() throws IOException, CacheException {
        logger.info("Initializing EhCache CacheManager");
        if (this.configLocation != null) {
            InputStream is = this.configLocation.getInputStream();
            try {
                this.cacheManager = (this.shared ? CacheManager.create(is) : new CacheManager(is));
            } finally {
                is.close();
            }
        } else {
            this.cacheManager = (this.shared ? CacheManager.create() : new CacheManager());
        }

    }


    public CacheManager getObject() {
        return this.cacheManager;
    }

    public Class<? extends CacheManager> getObjectType() {
        return (this.cacheManager != null ? this.cacheManager.getClass() : CacheManager.class);
    }

    public boolean isSingleton() {
        return true;
    }


    public void destroy() {
        logger.info("Shutting down EhCache CacheManager");
        this.cacheManager.shutdown();
    }

}
