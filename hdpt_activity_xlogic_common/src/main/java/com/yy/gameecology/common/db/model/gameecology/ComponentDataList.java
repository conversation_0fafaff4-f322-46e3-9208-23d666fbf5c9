package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-02-18 14:16
 **/
@Data
@TableColumn(underline = true)
public class ComponentDataList implements Serializable {


    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    public static String TABLE_NAME = "component_data_list_";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<ComponentDataList> ROW_MAPPER = null;

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * component_id
     */
    private Long componentId;

    /**
     * cmpt_use_inx
     */
    private Integer cmptUseInx;

    /**
     * seq
     */
    private String seq;

    /**
     * key_name
     */
    private String keyName;

    /**
     * value
     */
    private String value;

    /**
     * create_time
     */
    private Date createTime;
}
