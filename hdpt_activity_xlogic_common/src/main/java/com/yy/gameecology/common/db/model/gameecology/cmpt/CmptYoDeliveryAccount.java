package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@TableColumn(underline = true)
public class CmptYoDeliveryAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<CmptYoDeliveryAccount> ROW_MAPPER = (rs, rowNum) -> {
        CmptYoDeliveryAccount result = new CmptYoDeliveryAccount();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getLong("cmpt_use_inx"));
        result.setUid(rs.getLong("uid"));
        result.setTp(rs.getInt("tp"));
        result.setCnt(rs.getInt("cnt"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    private Long id;
    private Long actId;
    private Long cmptUseInx;
    private Long uid;
    private Integer tp;
    private Integer cnt; // 包裹数量
    private Date createTime;
    private Date updateTime;
}
